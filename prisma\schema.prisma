// 简易号池管理系统 (EasyPool) - 数据模型定义
// 基于 TiDB Cloud (MySQL 兼容) 的数据库设计

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider     = "mysql"
  url          = env("DATABASE_URL")
  relationMode = "prisma" // 推荐用于TiDB Cloud Serverless
}

// 账号状态枚举
enum AccountStatus {
  Available // 可用
  InUse     // 占用中
  Invalid   // 失效
  Failed    // 失败
  Expired   // 已过期
}

// 号池类型模型 - 顶级分类
model PoolType {
  id    String @id @default(cuid())
  name  String @unique
  pools Pool[]

  @@map("pool_types")
}

// 号池模型 - 具体的账号容器
model Pool {
  id          String   @id @default(cuid())
  name        String   @unique
  description String?  @db.Text // 号池描述信息（可选）
  poolTypeId  String
  poolType    PoolType @relation(fields: [poolTypeId], references: [id], onDelete: Cascade)
  accounts    Account[]

  @@map("pools")
}

// 账号模型 - 号池中的基本单元
model Account {
  id         String        @id @default(cuid())
  content    String        @db.Text // 使用Text类型以支持较长的JSON字符串
  status     AccountStatus @default(Available)
  expiresAt  DateTime?     // 过期时间（可选）
  notes      String?       @db.Text // 备注信息
  occupiedBy String?       @db.VarChar(255) // 占用该账号的用户标识（API密钥）
  poolId     String
  pool       Pool          @relation(fields: [poolId], references: [id], onDelete: Cascade)
  createdAt  DateTime      @default(now())
  updatedAt  DateTime      @updatedAt
  lastUsedAt DateTime?     // 最后使用时间

  @@map("accounts")
}

// 管理员模型 - 系统管理员
model Admin {
  id           String         @id @default(cuid())
  username     String         @unique
  passwordHash String         @db.VarChar(255)
  email        String?        @db.VarChar(255)
  createdAt    DateTime       @default(now())
  updatedAt    DateTime       @updatedAt
  lastLoginAt  DateTime?
  sessions     AdminSession[]

  @@map("admins")
}

// 用户模型 - API用户
model User {
  id           String        @id @default(cuid())
  name         String        @db.VarChar(100)
  email        String?       @unique @db.VarChar(255)
  passwordHash String?       @db.VarChar(255)
  status       String        @default("Active") // Active, Disabled
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt
  lastLoginAt  DateTime?
  apiKeys      ApiKey[]
  sessions     UserSession[]

  @@map("users")
}

// 用户会话模型
model UserSession {
  id        String   @id @default(cuid())
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  token     String   @unique
  expiresAt DateTime
  createdAt DateTime @default(now())

  @@map("user_sessions")
}

// 管理员会话模型
model AdminSession {
  id        String   @id @default(cuid())
  adminId   String
  token     String   @unique @default(cuid())
  expiresAt DateTime
  createdAt DateTime @default(now())
  admin     Admin    @relation(fields: [adminId], references: [id], onDelete: Cascade)

  @@map("admin_sessions")
}

// API密钥模型 - 系统认证 (扩展)
model ApiKey {
  id          Int      @id @default(autoincrement())
  key         String   @unique @default(cuid())
  name        String   @default("Default API Key")
  description String?
  status      String   @default("Active") // Active, Disabled
  permissions String?  @db.Text // JSON格式的权限配置
  userId      String?  // 关联的用户ID
  user        User?    @relation(fields: [userId], references: [id], onDelete: Cascade)
  expiresAt   DateTime?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @default(now()) @updatedAt
  lastUsedAt  DateTime?

  @@map("api_keys")
}
