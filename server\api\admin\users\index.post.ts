/**
 * 创建用户接口
 * POST /api/admin/users
 */

import { prisma } from '../../../utils/db'

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event)
    
    // 验证请求数据
    if (!body.name || typeof body.name !== 'string') {
      throw createError({
        statusCode: 400,
        statusMessage: 'User name is required'
      })
    }

    const name = body.name.trim()
    if (name.length === 0 || name.length > 100) {
      throw createError({
        statusCode: 400,
        statusMessage: 'User name must be between 1 and 100 characters'
      })
    }

    // 验证邮箱（如果提供）
    let email = null
    if (body.email && typeof body.email === 'string') {
      email = body.email.trim()
      if (email.length > 255) {
        throw createError({
          statusCode: 400,
          statusMessage: 'Email must be less than 255 characters'
        })
      }
      
      // 简单的邮箱格式验证
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(email)) {
        throw createError({
          statusCode: 400,
          statusMessage: 'Invalid email format'
        })
      }
    }

    // 检查邮箱是否已存在
    if (email) {
      const existingUser = await prisma.user.findFirst({
        where: { email }
      })
      
      if (existingUser) {
        throw createError({
          statusCode: 409,
          statusMessage: 'User with this email already exists'
        })
      }
    }

    // 创建用户
    const user = await prisma.user.create({
      data: {
        name,
        email,
        status: 'Active'
      },
      include: {
        apiKeys: true
      }
    })

    return {
      success: true,
      data: user,
      message: 'User created successfully'
    }
  } catch (error) {
    console.error('Create user error:', error)
    
    if (error.statusCode) {
      throw error
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to create user'
    })
  }
})
