/**
 * 认证相关工具函数
 */

import bcrypt from 'bcryptjs'
import { prisma } from './db'
import type { AdminInfo, SessionInfo, UserInfo, Permission, ApiKeyInfo } from '~/types/auth'
import type { H3Event } from 'h3'

// 密码哈希
export async function hashPassword(password: string): Promise<string> {
  const saltRounds = 12
  return bcrypt.hash(password, saltRounds)
}

// 密码验证
export async function verifyPassword(password: string, hash: string): Promise<boolean> {
  return bcrypt.compare(password, hash)
}

// 生成会话Token
export function generateSessionToken(): string {
  return crypto.randomUUID()
}

// 创建管理员会话
export async function createAdminSession(adminId: string): Promise<SessionInfo> {
  const token = generateSessionToken()
  const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000) // 24小时后过期

  const session = await prisma.adminSession.create({
    data: {
      adminId,
      token,
      expiresAt
    },
    include: {
      admin: true
    }
  })

  return {
    id: session.id,
    adminId: session.adminId,
    token: session.token,
    expiresAt: session.expiresAt,
    admin: {
      id: session.admin.id,
      username: session.admin.username,
      email: session.admin.email,
      lastLoginAt: session.admin.lastLoginAt
    }
  }
}

// 验证管理员会话
export async function validateAdminSession(token: string): Promise<SessionInfo | null> {
  try {
    const session = await prisma.adminSession.findUnique({
      where: { token },
      include: {
        admin: true
      }
    })

    if (!session) {
      return null
    }

    // 检查会话是否过期
    if (session.expiresAt < new Date()) {
      // 删除过期会话
      await prisma.adminSession.delete({
        where: { id: session.id }
      })
      return null
    }

    return {
      id: session.id,
      adminId: session.adminId,
      token: session.token,
      expiresAt: session.expiresAt,
      admin: {
        id: session.admin.id,
        username: session.admin.username,
        email: session.admin.email,
        lastLoginAt: session.admin.lastLoginAt
      }
    }
  } catch (error) {
    console.error('Error validating admin session:', error)
    return null
  }
}

// 删除管理员会话
export async function deleteAdminSession(token: string): Promise<void> {
  try {
    await prisma.adminSession.delete({
      where: { token }
    })
  } catch (error) {
    console.error('Error deleting admin session:', error)
  }
}

// 管理员登录
export async function loginAdmin(username: string, password: string): Promise<AdminInfo | null> {
  try {
    const admin = await prisma.admin.findUnique({
      where: { username }
    })

    if (!admin) {
      return null
    }

    const isValidPassword = await verifyPassword(password, admin.passwordHash)
    if (!isValidPassword) {
      return null
    }

    // 更新最后登录时间
    await prisma.admin.update({
      where: { id: admin.id },
      data: { lastLoginAt: new Date() }
    })

    return {
      id: admin.id,
      username: admin.username,
      email: admin.email,
      lastLoginAt: new Date()
    }
  } catch (error) {
    console.error('Error during admin login:', error)
    return null
  }
}

// 创建默认管理员账号
export async function createDefaultAdmin(): Promise<void> {
  try {
    // 先检查是否存在jiwei用户，如果存在则更新密码
    const existingJiwei = await prisma.admin.findUnique({
      where: { username: 'jiwei' }
    })

    if (existingJiwei) {
      // 更新jiwei用户的密码
      const newPassword = 'jiweiep0703'
      const newPasswordHash = await hashPassword(newPassword)

      await prisma.admin.update({
        where: { id: existingJiwei.id },
        data: { passwordHash: newPasswordHash }
      })

      console.log('Default admin password updated: username=jiwei, password=jiweiep0703')
      return
    }

    // 检查是否有其他管理员
    const existingAdmin = await prisma.admin.findFirst()
    if (existingAdmin) {
      return // 已存在其他管理员，不需要创建
    }

    const defaultPassword = 'jiweiep0703'
    const passwordHash = await hashPassword(defaultPassword)

    await prisma.admin.create({
      data: {
        username: 'jiwei',
        passwordHash,
        email: '<EMAIL>'
      }
    })

    console.log('Default admin created: username=jiwei, password=jiweiep0703')
  } catch (error) {
    console.error('Error creating default admin:', error)
  }
}

// 验证API密钥（新版本）
export async function validateApiKeyNew(key: string): Promise<ApiKeyInfo | null> {
  try {
    const apiKey = await prisma.apiKey.findUnique({
      where: { key },
      include: {
        user: true
      }
    })

    if (!apiKey) {
      return null
    }

    // 检查API密钥状态
    if (apiKey.status !== 'Active') {
      return null
    }

    // 检查是否过期
    if (apiKey.expiresAt && apiKey.expiresAt < new Date()) {
      return null
    }

    // 更新最后使用时间
    await prisma.apiKey.update({
      where: { id: apiKey.id },
      data: { lastUsedAt: new Date() }
    })

    // 解析权限
    let permissions: Permission[] = []
    if (apiKey.permissions) {
      try {
        permissions = JSON.parse(apiKey.permissions)
      } catch (error) {
        console.error('Error parsing API key permissions:', error)
      }
    }

    return {
      id: apiKey.id,
      key: apiKey.key,
      name: apiKey.name,
      description: apiKey.description,
      status: apiKey.status,
      permissions,
      userId: apiKey.userId,
      user: apiKey.user ? {
        id: apiKey.user.id,
        name: apiKey.user.name,
        email: apiKey.user.email,
        status: apiKey.user.status,
        permissions
      } : undefined,
      expiresAt: apiKey.expiresAt,
      lastUsedAt: apiKey.lastUsedAt
    }
  } catch (error) {
    console.error('Error validating API key:', error)
    return null
  }
}

// 管理员认证中间件
export async function requireAdminAuth(event: H3Event): Promise<void> {
  const token = getCookie(event, 'admin_token')

  if (!token) {
    throw createError({
      statusCode: 401,
      statusMessage: 'Admin authentication required'
    })
  }

  const session = await validateAdminSession(token)
  if (!session) {
    throw createError({
      statusCode: 401,
      statusMessage: 'Invalid or expired session'
    })
  }

  // 将管理员信息添加到事件上下文
  event.context.admin = session.admin
}

// API密钥认证中间件
export async function requireApiKey(event: H3Event): Promise<void> {
  const apiKey = getHeader(event, 'x-api-key') || getHeader(event, 'X-API-KEY')

  if (!apiKey) {
    throw createError({
      statusCode: 401,
      statusMessage: 'API key is required. Please provide X-API-KEY header.'
    })
  }

  const keyInfo = await validateApiKeyNew(apiKey)
  if (!keyInfo) {
    throw createError({
      statusCode: 401,
      statusMessage: 'Invalid API key'
    })
  }

  // 将用户信息和权限添加到事件上下文
  event.context.user = keyInfo.user
  event.context.permissions = keyInfo.permissions
  event.context.apiKey = keyInfo
}

// 权限检查中间件
export function requirePermission(permission: Permission) {
  return async (event: H3Event) => {
    const permissions = event.context.permissions || []
    if (!permissions.includes(permission)) {
      throw createError({
        statusCode: 403,
        statusMessage: `Insufficient permissions. Required: ${permission}`
      })
    }
  }
}

// 检查权限
export function hasPermission(userPermissions: Permission[], requiredPermission: Permission): boolean {
  return userPermissions.includes(requiredPermission)
}

// 检查多个权限
export function hasPermissions(userPermissions: Permission[], requiredPermissions: Permission[]): boolean {
  return requiredPermissions.every(permission => userPermissions.includes(permission))
}

// 兼容旧的API密钥验证函数
export async function validateApiKey(key: string): Promise<boolean> {
  const keyInfo = await validateApiKeyNew(key)
  return keyInfo !== null
}

// 从请求中提取API密钥
export function extractApiKey(event: H3Event): string | undefined {
  return getHeader(event, 'x-api-key') || getHeader(event, 'X-API-KEY')
}
