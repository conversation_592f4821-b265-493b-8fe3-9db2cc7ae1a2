import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function updateAdmin() {
  try {
    console.log('正在更新管理员账号...')
    
    // 查找现有的管理员账号
    const existingAdmin = await prisma.admin.findFirst()
    
    if (!existingAdmin) {
      console.log('没有找到管理员账号')
      return
    }

    console.log('找到管理员账号:', existingAdmin.username)

    // 生成新密码的哈希
    const newPassword = 'jiweiep0703'
    const saltRounds = 12
    const newPasswordHash = await bcrypt.hash(newPassword, saltRounds)

    // 更新管理员账号
    await prisma.admin.update({
      where: { id: existingAdmin.id },
      data: {
        username: 'jiwei',
        passwordHash: newPasswordHash,
        email: '<EMAIL>'
      }
    })

    console.log('✅ 管理员账号已更新:')
    console.log('   用户名: jiwei')
    console.log('   密码: jiweiep0703')
    console.log('   邮箱: <EMAIL>')

  } catch (error) {
    console.error('❌ 更新失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

updateAdmin()