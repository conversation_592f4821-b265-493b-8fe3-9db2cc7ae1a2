/**
 * API认证中间件
 * 保护所有 /api/v1/* 路由，要求API密钥认证
 */

import { validateApiKeyNew } from '../utils/auth'
import { Permission } from '~/types/auth'

export default defineEventHandler(async (event) => {
  const url = getRequestURL(event)
  
  // 只处理v1 API路由
  if (!url.pathname.startsWith('/api/v1/')) {
    return
  }

  // 健康检查和文档接口不需要认证
  const publicRoutes = [
    '/api/v1/health',
    '/api/v1/docs'
  ]

  if (publicRoutes.includes(url.pathname)) {
    return
  }

  // 验证API密钥
  const apiKey = getHeader(event, 'x-api-key') || getHeader(event, 'X-API-KEY')
  
  if (!apiKey) {
    throw createError({
      statusCode: 401,
      statusMessage: 'API key is required. Please provide X-API-KEY header.'
    })
  }

  const keyInfo = await validateApiKeyNew(apiKey)
  if (!keyInfo) {
    throw createError({
      statusCode: 401,
      statusMessage: 'Invalid or expired API key'
    })
  }

  // 将用户信息和权限添加到事件上下文
  event.context.user = keyInfo.user
  event.context.permissions = keyInfo.permissions || []
  event.context.apiKey = keyInfo

  // 根据路由检查权限
  const method = event.node.req.method?.toUpperCase()
  const pathname = url.pathname

  // 定义路由权限映射
  const routePermissions: Record<string, Permission[]> = {
    // 账号相关
    'GET:/api/v1/account': [Permission.ACCOUNT_READ],
    'POST:/api/v1/account': [Permission.ACCOUNT_CREATE],
    'PUT:/api/v1/account': [Permission.ACCOUNT_UPDATE],
    'DELETE:/api/v1/account': [Permission.ACCOUNT_DELETE],
    
    // 统计相关
    'GET:/api/v1/stats': [Permission.STATS_READ],
  }

  const routeKey = `${method}:${pathname}`
  const requiredPermissions = routePermissions[routeKey]

  if (requiredPermissions) {
    const userPermissions = event.context.permissions as Permission[]
    const hasRequiredPermissions = requiredPermissions.every(permission => 
      userPermissions.includes(permission)
    )

    if (!hasRequiredPermissions) {
      throw createError({
        statusCode: 403,
        statusMessage: `Insufficient permissions. Required: ${requiredPermissions.join(', ')}`
      })
    }
  }
})
