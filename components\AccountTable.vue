<template>
  <div class="space-y-4">
    <!-- 筛选和搜索 -->
    <div class="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
      <div class="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
        <!-- 状态筛选 -->
        <select
          v-model="filters.status"
          class="rounded-lg border-gray-300 text-sm focus:border-blue-500 focus:ring-blue-500"
          @change="handleFilterChange"
        >
          <option value="">全部状态</option>
          <option value="Available">可用</option>
          <option value="InUse">占用中</option>
          <option value="Invalid">失效</option>
          <option value="Failed">失败</option>
          <option value="Expired">过期</option>
        </select>

        <!-- 搜索框 -->
        <UiInput
          v-model="filters.search"
          placeholder="搜索账号内容或备注..."
          size="sm"
          clearable
          @input="handleSearchChange"
        >
          <template #prefix>
            <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </template>
        </UiInput>
      </div>

      <!-- 操作按钮 -->
      <div class="flex gap-2">
        <!-- 批量操作按钮 -->
        <div v-if="selectedAccounts.length > 0" class="flex gap-2 mr-4">
          <select
            v-model="batchStatus"
            class="rounded-lg border-gray-300 text-sm focus:border-blue-500 focus:ring-blue-500"
          >
            <option value="">选择状态</option>
            <option value="Available">可用</option>
            <option value="InUse">占用中</option>
            <option value="Invalid">失效</option>
            <option value="Expired">过期</option>
          </select>

          <UiButton
            size="sm"
            variant="warning"
            :disabled="!batchStatus"
            @click="handleBatchUpdateStatus"
          >
            批量修改状态 ({{ selectedAccounts.length }})
          </UiButton>

          <UiButton
            size="sm"
            variant="danger"
            @click="handleBatchDelete"
          >
            批量删除 ({{ selectedAccounts.length }})
          </UiButton>
        </div>

        <UiButton size="sm" @click="handleRefresh">
          <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
          刷新
        </UiButton>

        <UiButton size="sm" variant="success" @click="handleAddAccount">
          <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          添加账号
        </UiButton>
      </div>
    </div>

    <!-- 表格 -->
    <UiTable
      :columns="columns"
      :data="accounts"
      :loading="loading"
      :sort-by="sortBy"
      :sort-order="sortOrder"
      empty-text="暂无账号数据"
      @sort-change="handleSort"
      @row-click="handleRowClick"
    >
      <!-- 复选框列 -->
      <template #select="{ row }">
        <input
          type="checkbox"
          :checked="selectedAccounts.includes(row.id)"
          @change="toggleAccountSelection(row.id)"
          class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
        />
      </template>
      <!-- 账号内容列 -->
      <template #content="{ value, row }">
        <div class="max-w-xs">
          <div class="font-mono text-sm truncate" :title="value">
            {{ truncateText(value, 30) }}
          </div>
          <div v-if="row.notes" class="text-xs text-gray-500 truncate mt-1" :title="row.notes">
            {{ truncateText(row.notes, 40) }}
          </div>
        </div>
      </template>

      <!-- 状态列 -->
      <template #status="{ value }">
        <span
          :class="getStatusClasses(value)"
          class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
        >
          {{ getStatusText(value) }}
        </span>
      </template>

      <!-- 过期时间列 -->
      <template #expiresAt="{ value }">
        <div v-if="value" class="text-sm">
          <div :class="getExpirationClasses(value)">
            {{ formatDate(value, 'datetime') }}
          </div>
          <div class="text-xs text-gray-500 mt-1">
            {{ getExpirationText(value) }}
          </div>
        </div>
        <span v-else class="text-gray-400">永不过期</span>
      </template>

      <!-- 最后使用时间列 -->
      <template #lastUsedAt="{ value }">
        <span v-if="value" class="text-sm text-gray-600">
          {{ formatDate(value, 'relative') }}
        </span>
        <span v-else class="text-gray-400">从未使用</span>
      </template>

      <!-- 操作列 -->
      <template #actions="{ row }">
        <div class="flex items-center gap-1">
          <!-- 状态操作按钮 -->
          <UiButton
            v-if="row.status === 'InUse'"
            size="xs"
            variant="warning"
            @click="handleRelease(row)"
          >
            释放
          </UiButton>
          
          <UiButton
            v-else-if="row.status === 'Invalid'"
            size="xs"
            variant="success"
            @click="handleReactivate(row)"
          >
            重新激活
          </UiButton>

          <!-- 编辑按钮 -->
          <UiButton
            size="xs"
            variant="ghost"
            @click="handleEdit(row)"
          >
            <svg class="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
          </UiButton>

          <!-- 删除按钮 -->
          <UiButton
            size="xs"
            variant="ghost"
            @click="handleDelete(row)"
          >
            <svg class="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
          </UiButton>
        </div>
      </template>
    </UiTable>

    <!-- 分页 -->
    <div v-if="pagination && pagination.totalPages > 1" class="flex items-center justify-between">
      <div class="text-sm text-gray-700">
        显示第 {{ (pagination.currentPage - 1) * pagination.limit + 1 }} - 
        {{ Math.min(pagination.currentPage * pagination.limit, pagination.totalCount) }} 条，
        共 {{ pagination.totalCount }} 条
      </div>
      
      <div class="flex items-center gap-2">
        <UiButton
          size="sm"
          variant="ghost"
          :disabled="!pagination.hasPrevPage"
          @click="handlePageChange(pagination.currentPage - 1)"
        >
          上一页
        </UiButton>
        
        <span class="text-sm text-gray-600">
          第 {{ pagination.currentPage }} / {{ pagination.totalPages }} 页
        </span>
        
        <UiButton
          size="sm"
          variant="ghost"
          :disabled="!pagination.hasNextPage"
          @click="handlePageChange(pagination.currentPage + 1)"
        >
          下一页
        </UiButton>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Account, AccountFilters, TableColumn, PaginationInfo } from '~/types'

interface Props {
  accounts: Account[]
  loading?: boolean
  pagination?: PaginationInfo
  filters?: AccountFilters
}

interface Emits {
  refresh: []
  addAccount: []
  editAccount: [account: Account]
  deleteAccount: [account: Account]
  releaseAccount: [account: Account]
  reactivateAccount: [account: Account]
  filterChange: [filters: AccountFilters]
  sortChange: [sortBy: string, sortOrder: 'asc' | 'desc']
  pageChange: [page: number]
  rowClick: [account: Account]
  batchDelete: [accountIds: string[]]
  batchUpdateStatus: [accountIds: string[], status: string]
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  filters: () => ({})
})

const emit = defineEmits<Emits>()

// 响应式数据
const filters = ref<AccountFilters>({ ...props.filters })
const sortBy = ref('createdAt')
const sortOrder = ref<'asc' | 'desc'>('desc')

// 批量操作相关
const selectedAccounts = ref<string[]>([])
const batchStatus = ref('')

// 搜索防抖
let searchTimeout: NodeJS.Timeout | null = null

// 表格列配置
const columns: TableColumn[] = [
  { key: 'select', title: '', sortable: false, width: '50px', align: 'center' },
  { key: 'content', title: '账号内容', sortable: false, width: '300px' },
  { key: 'status', title: '状态', sortable: true, width: '100px', align: 'center' },
  { key: 'expiresAt', title: '过期时间', sortable: true, width: '180px' },
  { key: 'lastUsedAt', title: '最后使用', sortable: true, width: '120px' },
  { key: 'createdAt', title: '创建时间', sortable: true, width: '180px' },
  { key: 'actions', title: '操作', sortable: false, width: '120px', align: 'center' }
]

// 方法
const handleFilterChange = () => {
  emit('filterChange', { ...filters.value })
}

const handleSearchChange = () => {
  if (searchTimeout) {
    clearTimeout(searchTimeout)
  }
  
  searchTimeout = setTimeout(() => {
    emit('filterChange', { ...filters.value })
  }, 500)
}

const handleSort = (column: any, order: 'asc' | 'desc') => {
  sortBy.value = column.key
  sortOrder.value = order
  emit('sortChange', column.key, order)
}

const handlePageChange = (page: number) => {
  emit('pageChange', page)
}

const handleRowClick = (account: Account) => {
  emit('rowClick', account)
}

const handleRefresh = () => {
  emit('refresh')
}

const handleAddAccount = () => {
  emit('addAccount')
}

const handleEdit = (account: Account) => {
  emit('editAccount', account)
}

const handleDelete = (account: Account) => {
  emit('deleteAccount', account)
}

const handleRelease = (account: Account) => {
  emit('releaseAccount', account)
}

const handleReactivate = (account: Account) => {
  emit('reactivateAccount', account)
}

// 批量操作方法
const toggleAccountSelection = (accountId: string) => {
  const index = selectedAccounts.value.indexOf(accountId)
  if (index > -1) {
    selectedAccounts.value.splice(index, 1)
  } else {
    selectedAccounts.value.push(accountId)
  }
}

const handleBatchDelete = () => {
  if (selectedAccounts.value.length === 0) return

  if (confirm(`确定要删除选中的 ${selectedAccounts.value.length} 个账号吗？`)) {
    emit('batchDelete', [...selectedAccounts.value])
    selectedAccounts.value = []
  }
}

const handleBatchUpdateStatus = () => {
  if (selectedAccounts.value.length === 0 || !batchStatus.value) return

  if (confirm(`确定要将选中的 ${selectedAccounts.value.length} 个账号状态修改为 "${getStatusText(batchStatus.value)}" 吗？`)) {
    emit('batchUpdateStatus', [...selectedAccounts.value], batchStatus.value)
    selectedAccounts.value = []
    batchStatus.value = ''
  }
}



// 状态工具方法
const getStatusText = (status: string) => {
  const statusMap = {
    Available: '可用',
    InUse: '占用中',
    Invalid: '失效',
    Failed: '失败',
    Expired: '过期'
  }
  return statusMap[status as keyof typeof statusMap] || status
}

const getStatusClasses = (status: string) => {
  const statusMap = {
    Available: 'text-green-700 bg-green-100',
    InUse: 'text-blue-700 bg-blue-100',
    Invalid: 'text-red-700 bg-red-100',
    Failed: 'text-orange-700 bg-orange-100',
    Expired: 'text-gray-700 bg-gray-100'
  }
  return statusMap[status as keyof typeof statusMap] || 'text-gray-700 bg-gray-100'
}

// 工具方法
const formatDate = (dateString: string, format: string = 'datetime') => {
  if (!dateString) return '未知'
  const date = new Date(dateString)

  if (format === 'relative') {
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
    const diffDays = Math.floor(diffHours / 24)

    if (diffDays > 0) {
      return `${diffDays}天前`
    } else if (diffHours > 0) {
      return `${diffHours}小时前`
    } else {
      return '刚刚'
    }
  }

  return date.toLocaleString('zh-CN')
}

const truncateText = (text: string, maxLength: number) => {
  if (!text || text.length <= maxLength) return text
  return text.substring(0, maxLength) + '...'
}

const getExpirationClasses = (expiresAt: string) => {
  const now = new Date()
  const expiration = new Date(expiresAt)
  const diffHours = (expiration.getTime() - now.getTime()) / (1000 * 60 * 60)

  if (diffHours < 0) {
    return 'text-red-600 font-medium'
  } else if (diffHours < 24) {
    return 'text-yellow-600 font-medium'
  } else {
    return 'text-gray-600'
  }
}

const getExpirationText = (expiresAt: string) => {
  const now = new Date()
  const expiration = new Date(expiresAt)
  const diffHours = (expiration.getTime() - now.getTime()) / (1000 * 60 * 60)
  
  if (diffHours < 0) {
    return '已过期'
  } else if (diffHours < 1) {
    return '即将过期'
  } else if (diffHours < 24) {
    return `${Math.floor(diffHours)}小时后过期`
  } else {
    const diffDays = Math.floor(diffHours / 24)
    return `${diffDays}天后过期`
  }
}

// 监听props变化
watch(() => props.filters, (newFilters) => {
  filters.value = { ...newFilters }
}, { deep: true })
</script>
