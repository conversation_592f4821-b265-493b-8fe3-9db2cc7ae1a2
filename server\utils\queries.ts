/**
 * 数据库查询辅助工具
 * 提供常用的数据库查询方法和业务逻辑
 */

import { prisma } from './db'
import type { AccountStatus } from '@prisma/client'
import type { PoolStats, PoolTypeWithStats, ApiResponseStatus } from '~/types/database'

/**
 * 获取号池统计信息
 */
export async function getPoolStats(poolId: string): Promise<PoolStats> {
  const stats = await prisma.account.groupBy({
    by: ['status'],
    where: {
      poolId
    },
    _count: {
      status: true
    }
  })
  
  const result = {
    total: 0,
    available: 0,
    inUse: 0,
    invalid: 0,
    expired: 0
  }
  
  stats.forEach(stat => {
    result.total += stat._count.status
    switch (stat.status) {
      case 'Available':
        result.available = stat._count.status
        break
      case 'InUse':
        result.inUse = stat._count.status
        break
      case 'Invalid':
        result.invalid = stat._count.status
        break
      case 'Expired':
        result.expired = stat._count.status
        break
    }
  })
  
  return result
}

/**
 * 获取所有号池类型及其统计信息
 */
export async function getPoolTypesWithStats(): Promise<PoolTypeWithStats[]> {
  const poolTypes = await prisma.poolType.findMany({
    include: {
      pools: {
        include: {
          _count: {
            select: {
              accounts: true
            }
          }
        }
      }
    }
  })
  
  // 为每个号池类型计算统计信息
  const result = await Promise.all(
    poolTypes.map(async (poolType) => {
      let totalAccounts = 0
      let availableAccounts = 0
      
      for (const pool of poolType.pools) {
        const stats = await getPoolStats(pool.id)
        totalAccounts += stats.total
        availableAccounts += stats.available
      }
      
      return {
        ...poolType,
        stats: {
          totalPools: poolType.pools.length,
          totalAccounts,
          availableAccounts
        }
      }
    })
  )
  
  return result
}

/**
 * 获取可用账号（用于取号操作）
 * 使用事务确保原子性
 */
export async function getAvailableAccount(poolName: string) {
  return await prisma.$transaction(async (tx) => {
    // 1. 首先查找号池
    const pool = await tx.pool.findUnique({
      where: { name: poolName }
    })
    
    if (!pool) {
      throw new Error(`Pool '${poolName}' not found`)
    }
    
    // 2. 查找可用账号
    const account = await tx.account.findFirst({
      where: {
        poolId: pool.id,
        status: 'Available',
        OR: [
          { expiresAt: null },
          { expiresAt: { gt: new Date() } }
        ]
      },
      orderBy: {
        createdAt: 'asc' // 先进先出
      }
    })
    
    if (!account) {
      throw new Error(`No available accounts in pool '${poolName}'`)
    }
    
    // 3. 更新账号状态为占用中
    const updatedAccount = await tx.account.update({
      where: { id: account.id },
      data: {
        status: 'InUse',
        lastUsedAt: new Date()
      }
    })
    
    return updatedAccount
  })
}

/**
 * 上报账号状态
 */
export async function reportAccountStatus(
  accountId: string,
  result: ApiResponseStatus
): Promise<void> {
  const newStatus: AccountStatus = result === 'ok' ? 'Available' : 'Invalid'
  
  await prisma.account.update({
    where: { id: accountId },
    data: {
      status: newStatus,
      lastUsedAt: new Date()
    }
  })
}

/**
 * 强制释放账号（将InUse状态改为Available）
 */
export async function forceReleaseAccount(accountId: string): Promise<void> {
  await prisma.account.update({
    where: { 
      id: accountId,
      status: 'InUse' // 只能释放占用中的账号
    },
    data: {
      status: 'Available'
    }
  })
}

/**
 * 验证API密钥
 */
export async function validateApiKey(key: string): Promise<boolean> {
  try {
    const apiKey = await prisma.apiKey.findUnique({
      where: { key }
    })
    return !!apiKey
  } catch (error) {
    console.error('API key validation failed:', error)
    return false
  }
}
