<template>
  <div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">UI组件测试页面</h1>
        <p class="mt-2 text-gray-600">测试所有基础UI组件的显示和功能</p>
      </div>

      <div class="space-y-8">
        <!-- 按钮组件测试 -->
        <UiCard title="按钮组件" subtitle="测试不同变体和状态的按钮">
          <div class="space-y-4">
            <div class="flex flex-wrap gap-4">
              <UiButton variant="primary">主要按钮</UiButton>
              <UiButton variant="secondary">次要按钮</UiButton>
              <UiButton variant="danger">危险按钮</UiButton>
              <UiButton variant="success">成功按钮</UiButton>
              <UiButton variant="warning">警告按钮</UiButton>
              <UiButton variant="info">信息按钮</UiButton>
              <UiButton variant="ghost">幽灵按钮</UiButton>
            </div>
            
            <div class="flex flex-wrap gap-4">
              <UiButton size="xs">超小按钮</UiButton>
              <UiButton size="sm">小按钮</UiButton>
              <UiButton size="md">中等按钮</UiButton>
              <UiButton size="lg">大按钮</UiButton>
              <UiButton size="xl">超大按钮</UiButton>
            </div>
            
            <div class="flex flex-wrap gap-4">
              <UiButton :loading="true">加载中</UiButton>
              <UiButton disabled>禁用按钮</UiButton>
              <UiButton block>块级按钮</UiButton>
            </div>
          </div>
        </UiCard>

        <!-- 输入框组件测试 -->
        <UiCard title="输入框组件" subtitle="测试不同类型和状态的输入框">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <UiInput
              v-model="testInput1"
              label="基础输入框"
              placeholder="请输入内容"
              hint="这是一个提示信息"
            />
            
            <UiInput
              v-model="testInput2"
              label="必填输入框"
              placeholder="请输入内容"
              required
              clearable
            />
            
            <UiInput
              v-model="testInput3"
              label="错误状态"
              placeholder="请输入内容"
              error="这是一个错误信息"
            />
            
            <UiInput
              v-model="testInput4"
              label="带计数器"
              placeholder="请输入内容"
              :maxlength="50"
              show-counter
            />
          </div>
        </UiCard>

        <!-- 表格组件测试 -->
        <UiCard title="表格组件" subtitle="测试表格的显示和排序功能">
          <UiTable
            :columns="tableColumns"
            :data="tableData"
            :loading="tableLoading"
            :sort-by="sortBy"
            :sort-order="sortOrder"
            @sort="handleSort"
            @row-click="handleRowClick"
          >
            <template #cell-status="{ value }">
              <span
                :class="getStatusClasses(value)"
                class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
              >
                {{ value }}
              </span>
            </template>
            
            <template #actions="{ row }">
              <div class="flex gap-2">
                <UiButton size="xs" variant="info" @click="editRow(row)">编辑</UiButton>
                <UiButton size="xs" variant="danger" @click="deleteRow(row)">删除</UiButton>
              </div>
            </template>
          </UiTable>
        </UiCard>

        <!-- 加载组件测试 -->
        <UiCard title="加载组件" subtitle="测试不同类型的加载动画">
          <div class="grid grid-cols-2 md:grid-cols-4 gap-8">
            <div class="text-center">
              <UiLoading type="spinner" size="lg" />
              <p class="mt-2 text-sm text-gray-600">旋转器</p>
            </div>
            
            <div class="text-center">
              <UiLoading type="dots" size="lg" />
              <p class="mt-2 text-sm text-gray-600">点状</p>
            </div>
            
            <div class="text-center">
              <UiLoading type="pulse" size="lg" />
              <p class="mt-2 text-sm text-gray-600">脉冲</p>
            </div>
            
            <div class="text-center">
              <UiLoading type="bars" size="lg" />
              <p class="mt-2 text-sm text-gray-600">条状</p>
            </div>
          </div>
        </UiCard>

        <!-- 操作按钮 -->
        <UiCard title="交互测试" subtitle="测试模态框和通知功能">
          <div class="flex flex-wrap gap-4">
            <UiButton @click="showModal = true">打开模态框</UiButton>
            <UiButton variant="success" @click="showSuccessNotification">成功通知</UiButton>
            <UiButton variant="danger" @click="showErrorNotification">错误通知</UiButton>
            <UiButton variant="warning" @click="showWarningNotification">警告通知</UiButton>
            <UiButton variant="info" @click="showInfoNotification">信息通知</UiButton>
          </div>
        </UiCard>
      </div>
    </div>

    <!-- 模态框测试 -->
    <UiModal v-model:show="showModal" title="测试模态框" size="md">
      <p class="text-gray-600">这是一个测试模态框的内容。</p>
      
      <template #footer>
        <UiButton variant="secondary" @click="showModal = false">取消</UiButton>
        <UiButton @click="showModal = false">确认</UiButton>
      </template>
    </UiModal>

    <!-- 通知组件 -->
    <UiNotification />
  </div>
</template>

<script setup lang="ts">
import type { TableColumn } from '~/types'

// 页面标题
useHead({
  title: 'UI组件测试 - EasyPool'
})

// 响应式数据
const testInput1 = ref('')
const testInput2 = ref('')
const testInput3 = ref('')
const testInput4 = ref('')
const showModal = ref(false)
const tableLoading = ref(false)
const sortBy = ref('name')
const sortOrder = ref<'asc' | 'desc'>('asc')

// 表格配置
const tableColumns: TableColumn[] = [
  { key: 'name', label: '名称', sortable: true },
  { key: 'email', label: '邮箱', sortable: true },
  { key: 'status', label: '状态', sortable: false },
  { key: 'createdAt', label: '创建时间', sortable: true }
]

const tableData = ref([
  { id: 1, name: '张三', email: '<EMAIL>', status: '活跃', createdAt: '2023-01-01' },
  { id: 2, name: '李四', email: '<EMAIL>', status: '禁用', createdAt: '2023-01-02' },
  { id: 3, name: '王五', email: '<EMAIL>', status: '活跃', createdAt: '2023-01-03' }
])

// 通知功能
const { success, error, warning, info } = useNotification()

// 方法
const handleSort = (column: string, order: 'asc' | 'desc') => {
  sortBy.value = column
  sortOrder.value = order
  console.log('排序:', column, order)
}

const handleRowClick = (row: any, index: number) => {
  console.log('点击行:', row, index)
}

const editRow = (row: any) => {
  console.log('编辑:', row)
}

const deleteRow = (row: any) => {
  console.log('删除:', row)
}

const getStatusClasses = (status: string) => {
  return status === '活跃' 
    ? 'bg-green-100 text-green-800'
    : 'bg-red-100 text-red-800'
}

const showSuccessNotification = () => {
  success('操作成功', '这是一个成功通知的示例')
}

const showErrorNotification = () => {
  error('操作失败', '这是一个错误通知的示例')
}

const showWarningNotification = () => {
  warning('警告信息', '这是一个警告通知的示例')
}

const showInfoNotification = () => {
  info('提示信息', '这是一个信息通知的示例')
}
</script>
