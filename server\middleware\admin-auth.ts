/**
 * 管理员API认证中间件
 * 保护所有 /api/admin/* 路由（除了认证相关的）
 */

import { validateAdminSession } from '../utils/auth'

export default defineEventHandler(async (event) => {
  const url = getRequestURL(event)
  
  // 只处理管理员API路由
  if (!url.pathname.startsWith('/api/admin/')) {
    return
  }

  // 认证相关的API不需要验证
  const authRoutes = [
    '/api/admin/auth/login',
    '/api/admin/auth/me',
    '/api/admin/auth/logout'
  ]

  if (authRoutes.includes(url.pathname)) {
    return
  }

  // 验证管理员会话
  const token = getCookie(event, 'admin_token')
  
  if (!token) {
    throw createError({
      statusCode: 401,
      statusMessage: 'Admin authentication required'
    })
  }

  const session = await validateAdminSession(token)
  if (!session) {
    throw createError({
      statusCode: 401,
      statusMessage: 'Invalid or expired admin session'
    })
  }

  // 将管理员信息添加到事件上下文
  event.context.admin = session.admin
})
