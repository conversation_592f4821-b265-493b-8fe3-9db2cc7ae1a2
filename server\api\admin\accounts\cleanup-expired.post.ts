/**
 * 清理过期账号接口
 * POST /api/admin/accounts/cleanup-expired
 */

import { prisma, executeTransaction } from '../../../utils/db'

export default defineEventHandler(async (event) => {
  try {
    const now = new Date()

    // 使用事务确保操作的原子性
    const result = await executeTransaction(async (tx) => {
      // 1. 查找所有过期但状态不是 Expired 的账号
      const expiredAccounts = await tx.account.findMany({
        where: {
          expiresAt: {
            lte: now
          },
          status: {
            not: 'Expired'
          }
        },
        include: {
          pool: {
            include: {
              poolType: true
            }
          }
        }
      })

      if (expiredAccounts.length === 0) {
        return {
          updatedCount: 0,
          expiredAccounts: []
        }
      }

      // 2. 更新过期账号的状态
      const updateResult = await tx.account.updateMany({
        where: {
          id: {
            in: expiredAccounts.map(account => account.id)
          }
        },
        data: {
          status: 'Expired',
          lastUsedAt: now
        }
      })

      // 3. 按号池分组统计
      const poolStats = expiredAccounts.reduce((acc, account) => {
        const poolId = account.poolId
        if (!acc[poolId]) {
          acc[poolId] = {
            poolId,
            poolName: account.pool.name,
            poolTypeName: account.pool.poolType.name,
            count: 0,
            previousStatuses: {}
          }
        }
        acc[poolId].count++
        
        const prevStatus = account.status
        if (!acc[poolId].previousStatuses[prevStatus]) {
          acc[poolId].previousStatuses[prevStatus] = 0
        }
        acc[poolId].previousStatuses[prevStatus]++
        
        return acc
      }, {} as any)

      return {
        updatedCount: updateResult.count,
        expiredAccounts: expiredAccounts.map(account => ({
          id: account.id,
          content: account.content.substring(0, 20) + '...', // 部分显示
          previousStatus: account.status,
          expiresAt: account.expiresAt,
          poolId: account.poolId,
          poolName: account.pool.name
        })),
        poolStats: Object.values(poolStats)
      }
    })

    console.log(`Expired accounts cleanup completed: ${result.updatedCount} accounts updated`)

    return {
      success: true,
      message: `Successfully updated ${result.updatedCount} expired accounts`,
      data: {
        updatedCount: result.updatedCount,
        cleanupTime: now,
        poolStats: result.poolStats,
        // 只在开发环境返回详细信息
        ...(process.env.NODE_ENV === 'development' && {
          expiredAccounts: result.expiredAccounts
        })
      }
    }
  } catch (error) {
    console.error('Failed to cleanup expired accounts:', error)
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to cleanup expired accounts'
    })
  }
})
