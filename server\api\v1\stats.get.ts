/**
 * API使用统计接口
 * GET /api/v1/stats
 * 
 * 提供系统整体统计信息，用于监控和分析
 */

import { prisma } from '../../utils/db'
import { requireApiKey } from '../../utils/auth'

export default defineEventHandler(async (event) => {
  try {
    // 1. API密钥认证
    await requireApiKey(event)

    // 2. 获取查询参数
    const query = getQuery(event)
    const { poolType, poolName } = query

    // 3. 构建查询条件
    const whereCondition: any = {}
    
    if (poolName) {
      whereCondition.pool = {
        name: poolName as string
      }
    } else if (poolType) {
      whereCondition.pool = {
        poolType: {
          name: poolType as string
        }
      }
    }

    // 4. 获取账号统计信息
    const [
      totalAccounts,
      accountsByStatus,
      recentlyUsedAccounts,
      expiredAccounts
    ] = await Promise.all([
      // 总账号数
      prisma.account.count({ where: whereCondition }),
      
      // 按状态分组统计
      prisma.account.groupBy({
        by: ['status'],
        where: whereCondition,
        _count: {
          status: true
        }
      }),
      
      // 最近使用的账号数（24小时内）
      prisma.account.count({
        where: {
          ...whereCondition,
          lastUsedAt: {
            gte: new Date(Date.now() - 24 * 60 * 60 * 1000)
          }
        }
      }),
      
      // 过期账号数
      prisma.account.count({
        where: {
          ...whereCondition,
          expiresAt: {
            lte: new Date()
          },
          status: {
            not: 'Expired'
          }
        }
      })
    ])

    // 5. 处理状态统计
    const statusStats = {
      available: 0,
      inUse: 0,
      invalid: 0,
      expired: 0
    }

    accountsByStatus.forEach(stat => {
      switch (stat.status) {
        case 'Available':
          statusStats.available = stat._count.status
          break
        case 'InUse':
          statusStats.inUse = stat._count.status
          break
        case 'Invalid':
          statusStats.invalid = stat._count.status
          break
        case 'Expired':
          statusStats.expired = stat._count.status
          break
      }
    })

    // 6. 获取号池统计（如果没有指定特定号池）
    let poolStats = null
    if (!poolName && !poolType) {
      const pools = await prisma.pool.findMany({
        include: {
          poolType: true,
          _count: {
            select: {
              accounts: true
            }
          }
        }
      })

      poolStats = pools.map(pool => ({
        poolId: pool.id,
        poolName: pool.name,
        poolType: pool.poolType.name,
        totalAccounts: pool._count.accounts
      }))
    }

    // 7. 计算使用率
    const usageRate = totalAccounts > 0 
      ? Math.round((statusStats.inUse / totalAccounts) * 100) 
      : 0

    const availabilityRate = totalAccounts > 0 
      ? Math.round((statusStats.available / totalAccounts) * 100) 
      : 0

    // 8. 返回统计信息
    return {
      success: true,
      data: {
        summary: {
          totalAccounts,
          usageRate,
          availabilityRate,
          recentlyUsedAccounts,
          expiredAccountsNeedingUpdate: expiredAccounts
        },
        statusBreakdown: statusStats,
        filters: {
          poolType: poolType || null,
          poolName: poolName || null
        },
        ...(poolStats && { pools: poolStats }),
        timestamp: new Date().toISOString()
      }
    }
  } catch (error) {
    console.error('Failed to get API stats:', error)
    
    // 如果是已知的错误（如验证错误），直接抛出
    if (error.statusCode) {
      throw error
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to get API stats'
    })
  }
})
