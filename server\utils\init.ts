/**
 * 数据库初始化工具
 * 负责系统启动时的数据库初始化操作
 */

import { prisma, checkDatabaseConnection } from './db'
import type { SystemInitResult } from '~/types/database'

/**
 * 初始化API密钥
 * 确保系统始终有一个可用的API密钥
 */
async function initializeApiKey(): Promise<string> {
  try {
    // 检查是否已存在API密钥
    const existingKey = await prisma.apiKey.findFirst()
    
    if (existingKey) {
      console.log('API Key already exists:', existingKey.key.substring(0, 8) + '...')
      return existingKey.key
    }
    
    // 创建新的API密钥
    const newKey = await prisma.apiKey.create({
      data: {}
    })
    
    console.log('New API Key created:', newKey.key.substring(0, 8) + '...')
    return newKey.key
  } catch (error) {
    console.error('Failed to initialize API key:', error)
    throw error
  }
}

/**
 * 清理过期账号
 * 将过期的账号状态更新为Expired
 */
async function cleanupExpiredAccounts(): Promise<number> {
  try {
    const result = await prisma.account.updateMany({
      where: {
        expiresAt: {
          lt: new Date()
        },
        status: {
          not: 'Expired'
        }
      },
      data: {
        status: 'Expired'
      }
    })
    
    if (result.count > 0) {
      console.log(`Marked ${result.count} accounts as expired`)
    }
    
    return result.count
  } catch (error) {
    console.error('Failed to cleanup expired accounts:', error)
    throw error
  }
}

/**
 * 数据库完整性检查
 * 检查数据库结构和数据的完整性
 */
async function performIntegrityCheck(): Promise<boolean> {
  try {
    // 检查必要的表是否存在
    await prisma.poolType.findMany({ take: 1 })
    await prisma.pool.findMany({ take: 1 })
    await prisma.account.findMany({ take: 1 })
    await prisma.apiKey.findMany({ take: 1 })
    
    console.log('Database integrity check passed')
    return true
  } catch (error) {
    console.error('Database integrity check failed:', error)
    return false
  }
}

/**
 * 系统初始化主函数
 * 执行所有必要的初始化操作
 */
export async function initializeSystem(): Promise<SystemInitResult> {
  try {
    console.log('Starting system initialization...')
    
    // 1. 检查数据库连接
    const isConnected = await checkDatabaseConnection()
    if (!isConnected) {
      throw new Error('Database connection failed')
    }
    console.log('✅ Database connection verified')
    
    // 2. 执行完整性检查
    const integrityOk = await performIntegrityCheck()
    if (!integrityOk) {
      throw new Error('Database integrity check failed')
    }
    console.log('✅ Database integrity check passed')
    
    // 3. 初始化API密钥
    const apiKey = await initializeApiKey()
    console.log('✅ API key initialized')
    
    // 4. 清理过期账号
    const expiredCount = await cleanupExpiredAccounts()
    console.log('✅ Expired accounts cleanup completed')
    
    console.log('System initialization completed successfully')
    
    return {
      success: true,
      apiKey,
      expiredAccountsCount: expiredCount
    }
  } catch (error) {
    console.error('System initialization failed:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}
