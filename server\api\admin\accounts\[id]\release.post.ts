/**
 * 强制释放账号接口
 * POST /api/admin/accounts/[id]/release
 */

import { prisma } from '../../../../utils/db'

export default defineEventHandler(async (event) => {
  try {
    // 获取路由参数中的ID
    const id = getRouterParam(event, 'id')
    
    if (!id) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Account ID is required'
      })
    }

    // 验证ID格式
    if (typeof id !== 'string' || id.length < 10) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid account ID format'
      })
    }

    // 检查账号是否存在
    const account = await prisma.account.findUnique({
      where: { id },
      include: {
        pool: {
          include: {
            poolType: true
          }
        }
      }
    })

    if (!account) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Account not found'
      })
    }

    // 检查账号当前状态
    if (account.status !== 'InUse') {
      throw createError({
        statusCode: 400,
        statusMessage: `Account is not in use. Current status: ${account.status}`
      })
    }

    // 检查账号是否已过期
    const now = new Date()
    let newStatus = 'Available'
    
    if (account.expiresAt && account.expiresAt <= now) {
      newStatus = 'Expired'
    }

    // 强制释放账号
    const updatedAccount = await prisma.account.update({
      where: { id },
      data: {
        status: newStatus,
        lastUsedAt: now
      }
    })

    console.log(`Account forcibly released: ${id} from pool: ${account.pool.name}, new status: ${newStatus}`)

    return {
      success: true,
      message: 'Account released successfully',
      data: {
        id: updatedAccount.id,
        previousStatus: account.status,
        newStatus: updatedAccount.status,
        poolId: account.poolId,
        poolName: account.pool.name,
        poolTypeName: account.pool.poolType.name,
        releasedAt: updatedAccount.lastUsedAt,
        wasExpired: newStatus === 'Expired'
      }
    }
  } catch (error) {
    console.error('Failed to release account:', error)
    
    // 如果是已知的错误（如验证错误），直接抛出
    if (error.statusCode) {
      throw error
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to release account'
    })
  }
})
