/**
 * 获取号池下的账号列表接口
 * GET /api/admin/pools/[id]/accounts
 */

import { prisma } from '../../../../utils/db'

export default defineEventHandler(async (event) => {
  try {
    // 获取路由参数中的ID
    const poolId = getRouterParam(event, 'id')
    
    if (!poolId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Pool ID is required'
      })
    }

    // 验证ID格式
    if (typeof poolId !== 'string' || poolId.length < 10) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid pool ID format'
      })
    }

    // 获取查询参数
    const query = getQuery(event)
    const { 
      status, 
      page = '1', 
      limit = '20', 
      search,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = query

    // 验证分页参数
    const pageNum = parseInt(page as string)
    const limitNum = parseInt(limit as string)

    if (isNaN(pageNum) || pageNum < 1) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid page number'
      })
    }

    if (isNaN(limitNum) || limitNum < 1 || limitNum > 100) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid limit (must be between 1 and 100)'
      })
    }

    // 检查号池是否存在
    const pool = await prisma.pool.findUnique({
      where: { id: poolId },
      include: {
        poolType: true
      }
    })

    if (!pool) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Pool not found'
      })
    }

    // 构建查询条件
    const whereCondition: any = {
      poolId: poolId
    }

    // 状态筛选
    if (status && typeof status === 'string') {
      const validStatuses = ['Available', 'InUse', 'Invalid', 'Expired']
      if (validStatuses.includes(status)) {
        whereCondition.status = status
      }
    }

    // 搜索条件（搜索账号内容和备注）
    if (search && typeof search === 'string' && search.trim().length > 0) {
      const searchTerm = search.trim()
      whereCondition.OR = [
        {
          content: {
            contains: searchTerm
          }
        },
        {
          notes: {
            contains: searchTerm
          }
        }
      ]
    }

    // 构建排序条件
    const validSortFields = ['createdAt', 'lastUsedAt', 'status', 'expiresAt']
    const validSortOrders = ['asc', 'desc']
    
    const orderBy: any = {}
    if (validSortFields.includes(sortBy as string) && validSortOrders.includes(sortOrder as string)) {
      orderBy[sortBy as string] = sortOrder
    } else {
      orderBy.createdAt = 'desc'
    }

    // 获取账号列表和总数
    const [accounts, totalCount] = await Promise.all([
      prisma.account.findMany({
        where: whereCondition,
        orderBy,
        skip: (pageNum - 1) * limitNum,
        take: limitNum,
        select: {
          id: true,
          content: true,
          status: true,
          notes: true,
          expiresAt: true,
          createdAt: true,
          lastUsedAt: true
        }
      }),
      prisma.account.count({
        where: whereCondition
      })
    ])

    // 检查过期账号并更新状态
    const now = new Date()
    const expiredAccountIds = accounts
      .filter(account => 
        account.expiresAt && 
        account.expiresAt <= now && 
        account.status !== 'Expired'
      )
      .map(account => account.id)

    if (expiredAccountIds.length > 0) {
      await prisma.account.updateMany({
        where: {
          id: { in: expiredAccountIds }
        },
        data: {
          status: 'Expired'
        }
      })

      // 更新返回数据中的状态
      accounts.forEach(account => {
        if (expiredAccountIds.includes(account.id)) {
          account.status = 'Expired'
        }
      })
    }

    // 计算分页信息
    const totalPages = Math.ceil(totalCount / limitNum)
    const hasNextPage = pageNum < totalPages
    const hasPrevPage = pageNum > 1

    return {
      success: true,
      data: {
        accounts,
        pagination: {
          currentPage: pageNum,
          totalPages,
          totalCount,
          limit: limitNum,
          hasNextPage,
          hasPrevPage
        },
        pool: {
          id: pool.id,
          name: pool.name,
          poolType: {
            id: pool.poolType.id,
            name: pool.poolType.name
          }
        },
        filters: {
          status: status || null,
          search: search || null,
          sortBy,
          sortOrder
        },
        expiredAccountsUpdated: expiredAccountIds.length
      }
    }
  } catch (error) {
    console.error('Failed to get pool accounts:', error)
    
    // 如果是已知的错误（如验证错误），直接抛出
    if (error.statusCode) {
      throw error
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to get pool accounts'
    })
  }
})
