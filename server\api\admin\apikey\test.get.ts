/**
 * API密钥测试接口
 * GET /api/admin/apikey/test
 * 用于测试API密钥管理功能
 */

import { getApiKeyStats, getCurrentApiKey, maskApiKey } from '../../../utils/apikey'

export default defineEventHandler(async (event) => {
  try {
    // 获取API密钥统计信息
    const stats = await getApiKeyStats()
    
    // 获取当前API密钥详情
    const currentKey = await getCurrentApiKey()
    
    return {
      success: true,
      message: 'API key test completed',
      data: {
        stats,
        currentKey: currentKey ? {
          id: currentKey.id,
          maskedKey: maskApiKey(currentKey.key),
          createdAt: currentKey.createdAt,
          fullKey: currentKey.key // 仅在测试接口中返回完整密钥
        } : null,
        timestamp: new Date().toISOString()
      }
    }
  } catch (error) {
    console.error('API key test failed:', error)
    
    throw createError({
      statusCode: 500,
      statusMessage: 'API key test failed'
    })
  }
})
