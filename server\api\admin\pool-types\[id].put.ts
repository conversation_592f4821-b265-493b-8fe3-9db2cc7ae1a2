/**
 * 更新号池类型接口
 * PUT /api/admin/pool-types/[id]
 */

import { prisma } from '../../../utils/db'

export default defineEventHandler(async (event) => {
  try {
    // 获取路由参数中的ID
    const id = getRouterParam(event, 'id')
    
    if (!id) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Pool type ID is required'
      })
    }

    // 验证ID格式
    if (typeof id !== 'string' || id.length < 10) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid pool type ID format'
      })
    }

    // 获取请求体数据
    const body = await readBody(event)
    
    // 数据验证
    if (!body || !body.name) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Pool type name is required'
      })
    }

    const { name } = body

    // 验证名称格式
    if (typeof name !== 'string' || name.trim().length === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Pool type name must be a non-empty string'
      })
    }

    const trimmedName = name.trim()

    // 验证名称长度
    if (trimmedName.length > 50) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Pool type name must be 50 characters or less'
      })
    }

    // 检查号池类型是否存在
    const existingPoolType = await prisma.poolType.findUnique({
      where: { id }
    })

    if (!existingPoolType) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Pool type not found'
      })
    }

    // 检查新名称是否与其他号池类型冲突
    const nameConflict = await prisma.poolType.findFirst({
      where: {
        name: trimmedName,
        id: { not: id } // 排除当前号池类型
      }
    })

    if (nameConflict) {
      throw createError({
        statusCode: 409,
        statusMessage: 'Pool type with this name already exists'
      })
    }

    // 更新号池类型
    const updatedPoolType = await prisma.poolType.update({
      where: { id },
      data: {
        name: trimmedName
      }
    })

    console.log(`Pool type updated: ${existingPoolType.name} -> ${updatedPoolType.name} (ID: ${id})`)

    return {
      success: true,
      message: 'Pool type updated successfully',
      data: {
        id: updatedPoolType.id,
        name: updatedPoolType.name,
        previousName: existingPoolType.name
      }
    }
  } catch (error) {
    console.error('Failed to update pool type:', error)
    
    // 如果是已知的错误（如验证错误），直接抛出
    if (error.statusCode) {
      throw error
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to update pool type'
    })
  }
})
