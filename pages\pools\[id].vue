<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 页面头部 -->
    <div class="bg-white shadow-sm border-b">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between h-16">
          <div class="flex items-center space-x-4">
            <!-- 面包屑导航 -->
            <nav class="flex items-center space-x-2 text-sm">
              <NuxtLink to="/" class="text-gray-500 hover:text-gray-700">首页</NuxtLink>
              <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
              </svg>
              <span class="text-gray-900 font-medium">{{ pool?.name || '号池详情' }}</span>
            </nav>
          </div>
          <div class="flex items-center space-x-3">
            <button
              @click="$router.back()"
              class="flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
            >
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
              </svg>
              返回
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div v-if="loading" class="text-center py-12">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <p class="mt-2 text-gray-600">加载中...</p>
      </div>

      <div v-else-if="pool" class="space-y-6">
        <!-- 号池信息卡片 -->
        <div class="bg-white rounded-lg shadow p-6">
          <div class="flex items-center justify-between mb-6">
            <h2 class="text-lg font-medium text-gray-900">号池信息</h2>
            <div class="flex items-center space-x-2">
              <button
                @click="showBatchUploadModal = true"
                class="flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
              >
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                </svg>
                批量导入
              </button>
            </div>
          </div>

          <!-- 号池描述 -->
          <div v-if="pool && pool.description" class="mb-4 p-3 bg-gray-50 rounded-lg">
            <p class="text-sm text-gray-700">{{ pool.description }}</p>
          </div>
          
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <div class="text-center p-4 bg-blue-50 rounded-lg">
              <div class="text-2xl font-bold text-blue-600">{{ stats.total }}</div>
              <div class="text-sm text-gray-600">总账号</div>
            </div>
            <div class="text-center p-4 bg-green-50 rounded-lg">
              <div class="text-2xl font-bold text-green-600">{{ stats.available }}</div>
              <div class="text-sm text-gray-600">可用</div>
            </div>
            <div class="text-center p-4 bg-yellow-50 rounded-lg">
              <div class="text-2xl font-bold text-yellow-600">{{ stats.inUse }}</div>
              <div class="text-sm text-gray-600">占用中</div>
            </div>
            <div class="text-center p-4 bg-red-50 rounded-lg">
              <div class="text-2xl font-bold text-red-600">{{ stats.invalid + stats.expired }}</div>
              <div class="text-sm text-gray-600">失效/过期</div>
            </div>
          </div>

          <div v-if="pool" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm text-gray-600">
            <div>
              <span class="font-medium">号池类型：</span>
              {{ pool.poolType?.name }}
            </div>
            <div>
              <span class="font-medium">创建时间：</span>
              {{ formatDate(pool.createdAt) }}
            </div>
            <div>
              <span class="font-medium">最后更新：</span>
              {{ formatDate(pool.updatedAt) }}
            </div>
          </div>
        </div>

        <!-- 账号列表 -->
        <div class="bg-white rounded-lg shadow">
          <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
              <h2 class="text-lg font-medium text-gray-900">账号列表</h2>
              <div class="text-sm text-gray-500">
                共 {{ accountsData.pagination?.totalCount || accounts.length }} 个账号
              </div>
            </div>
          </div>
          <div class="p-6">
            <AccountTable
              :accounts="accounts"
              :loading="loading"
              :pagination="accountsData.pagination"
              :filters="accountsData.filters"
              @refresh="refreshData"
              @add-account="showAddAccountModal = true"
              @edit-account="editAccount"
              @delete-account="deleteAccount"
              @release-account="releaseAccount"
              @reactivate-account="reactivateAccount"
              @batch-delete="handleBatchDelete"
              @batch-update-status="handleBatchUpdateStatus"
              @filter-change="handleFilterChange"
              @sort-change="handleSortChange"
              @page-change="handlePageChange"
            />
          </div>
        </div>
      </div>

      <div v-else class="text-center py-12">
        <p class="text-gray-500">号池不存在或加载失败</p>
      </div>
    </div>

    <!-- 添加账号模态框 -->
    <div v-if="showAddAccountModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 w-full max-w-md">
        <h3 class="text-lg font-medium mb-4">{{ editingAccount ? '编辑账号' : '添加账号' }}</h3>
        <form @submit.prevent="handleAccountSubmit">
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">账号内容</label>
              <input
                v-model="accountForm.content"
                type="text"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="请输入账号内容"
              >
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">备注</label>
              <textarea
                v-model="accountForm.notes"
                rows="3"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="请输入备注信息（可选）"
              ></textarea>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">过期时间（可选）</label>
              <input
                v-model="accountForm.expiresAt"
                type="datetime-local"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="选择过期时间"
              >
              <p class="mt-1 text-sm text-gray-500">留空表示永不过期</p>
            </div>

            <!-- 状态选择（仅编辑时显示） -->
            <div v-if="editingAccount">
              <label class="block text-sm font-medium text-gray-700 mb-1">状态</label>
              <select
                v-model="accountForm.status"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="Available">可用</option>
                <option value="InUse">占用中</option>
                <option value="Invalid">失效</option>
                <option value="Failed">失败</option>
                <option value="Expired">过期</option>
              </select>
            </div>
          </div>
          <div class="flex justify-end space-x-3 mt-6">
            <button
              type="button"
              @click="closeAccountModal"
              class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
            >
              取消
            </button>
            <button
              type="submit"
              :disabled="!accountForm.content.trim()"
              class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              {{ editingAccount ? '更新' : '添加' }}
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- 批量导入模态框 -->
    <BatchUploadModal
      v-if="showBatchUploadModal"
      :show="showBatchUploadModal"
      :pool-id="poolId"
      @close="showBatchUploadModal = false"
      @submit="handleBatchUpload"
    />
  </div>
</template>

<script setup>
// 页面参数
const route = useRoute()
const poolId = route.params.id

// 响应式数据
const loading = ref(true)
const refreshing = ref(false)
const pool = ref(null)
const accounts = ref([])
const accountsData = ref({
  pagination: null,
  filters: {}
})
const showAddAccountModal = ref(false)
const showBatchUploadModal = ref(false)
const editingAccount = ref(null)
const accountForm = ref({
  content: '',
  notes: '',
  expiresAt: '',
  status: 'Available'
})

// 计算属性
const stats = computed(() => {
  if (!accounts.value || accounts.value.length === 0) {
    return { total: 0, available: 0, inUse: 0, invalid: 0, expired: 0 }
  }

  const stats = {
    total: accounts.value.length,
    available: 0,
    inUse: 0,
    invalid: 0,
    expired: 0
  }

  accounts.value.forEach(account => {
    const status = getAccountStatus(account)
    switch (status) {
      case 'Available':
        stats.available++
        break
      case 'InUse':
        stats.inUse++
        break
      case 'Invalid':
        stats.invalid++
        break
      case 'Expired':
        stats.expired++
        break
    }
  })

  return stats
})

// 方法

const formatDate = (dateString) => {
  if (!dateString) return '未知'
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN')
}

const loadData = async (filters = {}, page = 1, sortBy = 'createdAt', sortOrder = 'desc') => {
  try {
    loading.value = true

    // 加载号池信息
    const poolResponse = await $fetch(`/api/admin/pools/${poolId}`)
    if (poolResponse.success) {
      pool.value = poolResponse.data
    }

    // 构建查询参数
    const queryParams = new URLSearchParams({
      page: page.toString(),
      limit: '20',
      sortBy,
      sortOrder,
      ...filters
    })

    // 加载账号列表
    const accountsResponse = await $fetch(`/api/admin/pools/${poolId}/accounts?${queryParams}`)
    if (accountsResponse.success) {
      accounts.value = accountsResponse.data.accounts
      accountsData.value = {
        pagination: accountsResponse.data.pagination,
        filters: accountsResponse.data.filters
      }
    }
  } catch (error) {
    console.error('Failed to load data:', error)
  } finally {
    loading.value = false
  }
}

const refreshData = async () => {
  refreshing.value = true
  await loadData()
  refreshing.value = false
}

// 账号管理方法
const editAccount = (account) => {
  editingAccount.value = account
  accountForm.value = {
    content: account.content,
    notes: account.notes || '',
    expiresAt: account.expiresAt ? new Date(account.expiresAt).toISOString().slice(0, 16) : '',
    status: account.status
  }
  showAddAccountModal.value = true
}

const closeAccountModal = () => {
  showAddAccountModal.value = false
  editingAccount.value = null
  accountForm.value = {
    content: '',
    notes: '',
    expiresAt: '',
    status: 'Available'
  }
}

const handleAccountSubmit = async () => {
  try {
    if (editingAccount.value) {
      // 编辑模式：更新账号状态和基本信息
      const statusData = {
        status: accountForm.value.status,
        notes: accountForm.value.notes
      }

      // 更新状态
      await $fetch(`/api/admin/accounts/${editingAccount.value.id}/status`, {
        method: 'PUT',
        body: statusData
      })

      // 如果内容或过期时间有变化，也需要更新基本信息
      if (accountForm.value.content !== editingAccount.value.content ||
          accountForm.value.expiresAt !== (editingAccount.value.expiresAt ? new Date(editingAccount.value.expiresAt).toISOString().slice(0, 16) : '')) {
        const basicData = {
          content: accountForm.value.content,
          expiresAt: accountForm.value.expiresAt ? new Date(accountForm.value.expiresAt).toISOString() : null
        }

        await $fetch(`/api/admin/accounts/${editingAccount.value.id}`, {
          method: 'PUT',
          body: basicData
        })
      }
    } else {
      // 创建模式：创建新账号
      const data = {
        poolId: poolId,
        content: accountForm.value.content,
        notes: accountForm.value.notes,
        expiresAt: accountForm.value.expiresAt ? new Date(accountForm.value.expiresAt).toISOString() : null
      }

      await $fetch('/api/admin/accounts', {
        method: 'POST',
        body: data
      })
    }

    closeAccountModal()
    await loadData()
  } catch (error) {
    console.error('Failed to save account:', error)
  }
}

const deleteAccount = async (account) => {
  if (confirm(`确定要删除账号 "${account.content}" 吗？`)) {
    try {
      await $fetch(`/api/admin/accounts/${account.id}`, {
        method: 'DELETE'
      })
      await loadData()
    } catch (error) {
      console.error('Failed to delete account:', error)
    }
  }
}

const releaseAccount = async (account) => {
  try {
    await $fetch(`/api/admin/accounts/${account.id}/release`, {
      method: 'POST'
    })
    await loadData()
  } catch (error) {
    console.error('Failed to release account:', error)
  }
}

const reactivateAccount = async (account) => {
  try {
    await $fetch(`/api/admin/accounts/${account.id}/status`, {
      method: 'PUT',
      body: { status: 'Available' }
    })
    await loadData()
  } catch (error) {
    console.error('Failed to reactivate account:', error)
  }
}

const handleBatchUpload = async (batchData) => {
  try {
    const response = await $fetch('/api/admin/accounts/batch', {
      method: 'POST',
      body: batchData
    })

    if (response.success) {
      console.log(`批量导入成功：${response.data.successCount} 个账号`)
      showBatchUploadModal.value = false
      await loadData()
    }
  } catch (error) {
    console.error('Failed to batch upload accounts:', error)
  }
}

// 批量操作处理方法
const handleBatchDelete = async (accountIds) => {
  try {
    const response = await $fetch('/api/admin/accounts/batch-delete', {
      method: 'POST',
      body: { accountIds }
    })

    if (response.success) {
      console.log(`批量删除成功：${response.data.deletedCount} 个账号`)
      await loadData()
    }
  } catch (error) {
    console.error('Failed to batch delete accounts:', error)
    alert('批量删除失败，请重试')
  }
}

const handleBatchUpdateStatus = async (accountIds, status) => {
  try {
    const response = await $fetch('/api/admin/accounts/batch-status', {
      method: 'PUT',
      body: { accountIds, status }
    })

    if (response.success) {
      console.log(`批量修改状态成功：${response.data.updatedCount} 个账号`)
      await loadData()
    }
  } catch (error) {
    console.error('Failed to batch update account status:', error)
    alert('批量修改状态失败，请重试')
  }
}

// 表格事件处理方法
const handleFilterChange = (filters) => {
  loadData(filters)
}

const handleSortChange = (sortBy, sortOrder) => {
  loadData(accountsData.value.filters, accountsData.value.pagination?.currentPage || 1, sortBy, sortOrder)
}

const handlePageChange = (page) => {
  loadData(accountsData.value.filters, page)
}

// 判断账号是否过期
const isAccountExpired = (account) => {
  if (!account.expiresAt) return false
  return new Date(account.expiresAt) < new Date()
}

// 获取账号的实际状态（考虑过期时间）
const getAccountStatus = (account) => {
  if (isAccountExpired(account)) {
    return 'Expired'
  }
  return account.status
}

// 获取账号状态的显示文本
const getStatusText = (account) => {
  const status = getAccountStatus(account)
  switch (status) {
    case 'Available': return '可用'
    case 'InUse': return '占用中'
    case 'Invalid': return '失效'
    case 'Expired': return '已过期'
    default: return '未知'
  }
}

// 获取账号状态的样式类
const getStatusClass = (account) => {
  const status = getAccountStatus(account)
  switch (status) {
    case 'Available': return 'bg-green-100 text-green-800'
    case 'InUse': return 'bg-yellow-100 text-yellow-800'
    case 'Failed': return 'bg-red-100 text-red-800'
    case 'Expired': return 'bg-gray-100 text-gray-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

// 页面加载时获取数据
onMounted(() => {
  loadData()
})

// 页面元数据
useHead({
  title: computed(() => `${pool.value?.name || '号池详情'} - EasyPool`)
})
</script>
