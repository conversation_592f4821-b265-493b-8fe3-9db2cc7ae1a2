/**
 * 管理员登出接口
 * POST /api/admin/auth/logout
 */

import { deleteAdminSession } from '../../../utils/auth'

export default defineEventHandler(async (event) => {
  try {
    const token = getCookie(event, 'admin_token')
    
    if (token) {
      // 删除服务器端会话
      await deleteAdminSession(token)
    }

    // 清除客户端Cookie
    deleteCookie(event, 'admin_token')

    return {
      success: true,
      message: 'Logout successful'
    }
  } catch (error) {
    console.error('Logout error:', error)
    
    // 即使出错也要清除<PERSON><PERSON>
    deleteCookie(event, 'admin_token')
    
    return {
      success: true,
      message: 'Logout successful'
    }
  }
})
