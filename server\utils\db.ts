/**
 * 数据库连接工具类
 * 实现Prisma Client单例模式，避免在Serverless环境中连接耗尽问题
 */

import { PrismaClient } from '@prisma/client'
import type { DatabaseStats, TransactionCallback } from '~/types/database'

// 全局变量声明，用于在开发环境中保持连接
declare global {
  var __prisma: PrismaClient | undefined
}

// 数据库连接配置
const createPrismaClient = () => {
  return new PrismaClient({
    log: process.env.NODE_ENV === 'development' 
      ? ['query', 'info', 'warn', 'error'] 
      : ['error'],
    errorFormat: 'pretty',
  })
}

// 单例模式实现
const prisma = globalThis.__prisma ?? createPrismaClient()

// 在开发环境中保持连接，避免热重载时重复创建连接
if (process.env.NODE_ENV === 'development') {
  globalThis.__prisma = prisma
}

/**
 * 数据库连接健康检查
 */
export async function checkDatabaseConnection(): Promise<boolean> {
  try {
    await prisma.$queryRaw`SELECT 1`
    return true
  } catch (error) {
    console.error('Database connection failed:', error)
    return false
  }
}

/**
 * 优雅关闭数据库连接
 */
export async function disconnectDatabase(): Promise<void> {
  try {
    await prisma.$disconnect()
    console.log('Database connection closed gracefully')
  } catch (error) {
    console.error('Error closing database connection:', error)
  }
}

/**
 * 数据库事务执行器
 * 提供统一的事务处理接口
 */
export async function executeTransaction<T>(
  callback: TransactionCallback<T>
): Promise<T> {
  try {
    return await prisma.$transaction(callback)
  } catch (error) {
    console.error('Transaction failed:', error)
    throw error
  }
}

/**
 * 数据库连接统计信息
 */
export async function getDatabaseStats(): Promise<DatabaseStats> {
  try {
    const [
      poolTypes,
      pools,
      accounts,
      accountsByStatus,
      apiKeys
    ] = await Promise.all([
      prisma.poolType.count(),
      prisma.pool.count(),
      prisma.account.count(),
      // 按状态分组统计账号
      prisma.account.groupBy({
        by: ['status'],
        _count: {
          status: true
        }
      }),
      prisma.apiKey.count()
    ])

    // 处理状态统计
    const statusStats = {
      available: 0,
      inUse: 0,
      invalid: 0,
      expired: 0
    }

    accountsByStatus.forEach(stat => {
      switch (stat.status) {
        case 'Available':
          statusStats.available = stat._count.status
          break
        case 'InUse':
          statusStats.inUse = stat._count.status
          break
        case 'Invalid':
          statusStats.invalid = stat._count.status
          break
        case 'Expired':
          statusStats.expired = stat._count.status
          break
      }
    })

    return {
      poolTypes,
      pools,
      accounts,
      availableAccounts: statusStats.available,
      usedAccounts: statusStats.inUse,
      invalidAccounts: statusStats.invalid,
      expiredAccounts: statusStats.expired,
      apiKeys,
      timestamp: new Date().toISOString()
    }
  } catch (error) {
    console.error('Failed to get database stats:', error)
    throw error
  }
}

// 导出Prisma Client实例
export { prisma }
export default prisma
