/**
 * 全局认证中间件
 * 保护需要管理员权限的页面
 */

export default defineNuxtRouteMiddleware(async (to) => {
  // 公开路由，无需认证
  const publicRoutes = [
    '/admin/login'
  ]

  // 如果是公开路由，直接通过
  if (publicRoutes.includes(to.path)) {
    return
  }

  // 检查是否需要管理员认证的路由
  const adminRoutes = [
    '/',
    '/pools',
    '/api-keys',
    '/admin'
  ]

  const needsAuth = adminRoutes.some(route => 
    to.path === route || to.path.startsWith(route + '/')
  )

  if (!needsAuth) {
    return
  }

  // 在客户端检查认证状态
  if (process.client) {
    try {
      const response = await $fetch('/api/admin/auth/me')
      if (!response.success || !response.data) {
        return navigateTo('/admin/login')
      }
    } catch (error) {
      console.log('Auth check failed:', error)
      return navigateTo('/user/login')
    }
  }

  // 在服务端，我们依赖客户端的认证检查
  // 服务端的认证保护由API中间件处理
})
