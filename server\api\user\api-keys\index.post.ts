import { z } from 'zod'
import { prisma } from '~/server/utils/db'
import { generateApiKey } from '~/server/utils/crypto'

// 验证创建API密钥请求的schema
const createApiKeySchema = z.object({
  name: z.string().min(1, '密钥名称不能为空').max(100, '密钥名称过长'),
  description: z.string().optional().nullable()
})

export default defineEventHandler(async (event) => {
  try {
    // 获取用户token
    const token = getCookie(event, 'user_token')
    
    if (!token) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Not authenticated'
      })
    }

    // 查找有效的会话
    const session = await prisma.userSession.findUnique({
      where: { token },
      include: {
        user: {
          select: {
            id: true,
            status: true
          }
        }
      }
    })

    if (!session) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Invalid session'
      })
    }

    // 检查会话是否过期
    if (session.expiresAt < new Date()) {
      await prisma.userSession.delete({
        where: { id: session.id }
      })
      
      throw createError({
        statusCode: 401,
        statusMessage: 'Session expired'
      })
    }

    // 检查用户状态
    if (session.user.status !== 'Active') {
      throw createError({
        statusCode: 401,
        statusMessage: 'Account disabled'
      })
    }

    // 验证请求体
    const body = await readBody(event)
    const { name, description } = createApiKeySchema.parse(body)

    // 检查名称是否已存在（用户范围内）
    const existingApiKey = await prisma.apiKey.findFirst({
      where: { 
        name,
        userId: session.user.id
      }
    })

    if (existingApiKey) {
      throw createError({
        statusCode: 400,
        statusMessage: '密钥名称已存在'
      })
    }

    // 生成API密钥
    const key = generateApiKey()

    // 创建API密钥记录
    const apiKey = await prisma.apiKey.create({
      data: {
        name,
        key,
        description: description || null,
        userId: session.user.id,
        status: 'Active',
        // 普通用户创建的API密钥默认有基本权限
        permissions: JSON.stringify(['account:read', 'pool:read'])
      },
      select: {
        id: true,
        name: true,
        key: true,
        description: true,
        status: true,
        permissions: true,
        expiresAt: true,
        createdAt: true
      }
    })

    return {
      success: true,
      message: 'API密钥创建成功',
      data: apiKey
    }

  } catch (error) {
    console.error('Create user API key error:', error)
    
    if (error.name === 'ZodError') {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid request data',
        data: { message: error.errors[0]?.message || '请求数据无效' }
      })
    }

    if (error.statusCode) {
      throw error
    }

    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to create API key'
    })
  }
})
