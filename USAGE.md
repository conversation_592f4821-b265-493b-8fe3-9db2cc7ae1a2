# 使用文档

本文档详细介绍了简易号池管理系统的使用方法和功能说明。

## 目录

- [系统概述](#系统概述)
- [快速开始](#快速开始)
- [管理界面使用](#管理界面使用)
- [API接口使用](#api接口使用)
- [最佳实践](#最佳实践)
- [故障排除](#故障排除)

## 系统概述

简易号池管理系统（EasyPool）是一个轻量级的账号资源池管理平台，主要功能包括：

- **号池类型管理**：创建和管理不同类型的号池
- **号池管理**：创建、配置和监控号池
- **账号管理**：添加、导入、管理账号状态
- **API服务**：提供取号、归还等核心业务接口
- **系统监控**：实时监控系统状态和性能

## 快速开始

### 1. 访问系统
打开浏览器，访问系统地址（如：`https://your-domain.com`）

### 2. 生成API密钥
1. 点击右上角的设置按钮
2. 在"API密钥管理"区域点击"生成API密钥"
3. 复制并保存生成的API密钥（用于API调用）

### 3. 创建号池类型
1. 在首页点击"新建类型"按钮
2. 输入类型名称和描述
3. 点击"创建"完成

### 4. 创建号池
1. 在首页点击"新建号池"按钮
2. 选择号池类型，输入号池名称和描述
3. 点击"创建"完成

### 5. 添加账号
1. 点击号池卡片进入详情页
2. 点击"添加账号"按钮
3. 输入账号内容和相关信息
4. 点击"添加"完成

## 管理界面使用

### 首页功能

#### 统计概览
- **号池类型**：显示系统中的号池类型总数
- **号池总数**：显示创建的号池总数
- **账号总数**：显示所有号池中的账号总数
- **可用账号**：显示当前可用的账号数量和可用率

#### 搜索和筛选
- **搜索框**：支持按号池名称搜索
- **类型筛选**：按号池类型筛选显示
- **实时更新**：数据实时刷新显示

#### 号池操作
- **查看详情**：点击号池卡片查看详细信息
- **编辑号池**：点击编辑按钮修改号池信息
- **删除号池**：点击删除按钮删除号池（需确认）

### 号池详情页功能

#### 统计信息
- **总账号数**：号池中的账号总数
- **可用账号**：当前可用的账号数量
- **占用中**：正在使用的账号数量
- **失效账号**：已失效的账号数量
- **过期账号**：已过期的账号数量

#### 快速操作
- **添加单个账号**：手动添加一个账号
- **批量导入**：从文件或文本批量导入账号
- **导出账号**：将当前筛选的账号导出为CSV文件

#### 账号管理
- **搜索筛选**：按内容搜索，按状态筛选
- **状态管理**：释放占用中的账号，重新激活失效账号
- **批量操作**：批量删除、批量状态更新
- **分页浏览**：支持大量账号的分页显示

### 账号状态说明

| 状态 | 说明 | 操作 |
|------|------|------|
| Available | 可用 | 可以被API获取使用 |
| InUse | 占用中 | 正在被使用，不可重复获取 |
| Invalid | 失效 | 账号已失效，需要处理 |
| Expired | 过期 | 账号已过期，需要更新 |

### 批量导入功能

#### 支持格式
1. **文本输入**：每行一个账号
2. **文件上传**：支持 .txt 和 .csv 文件

#### 导入格式
```
# 基础格式
账号内容

# 带备注格式
账号内容|备注信息

# 完整格式
账号内容|备注信息|过期时间(YYYY-MM-DD HH:mm:ss)
```

#### 示例
```
<EMAIL>
<EMAIL>|测试账号
<EMAIL>|VIP账号|2024-12-31 23:59:59
```

## API接口使用

### 认证方式
所有API请求需要在请求头中添加API密钥：
```
X-API-KEY: your_api_key_here
```

### 核心业务接口

#### 1. 获取账号（取号）
```http
GET /api/v1/account?poolName=池名称
GET /api/v1/account?poolType=池类型
```

**请求参数：**
- `poolName`：号池名称（与poolType二选一）
- `poolType`：号池类型名称（与poolName二选一）

**响应示例：**
```json
{
  "success": true,
  "data": {
    "id": "account-id",
    "content": "账号内容",
    "poolName": "测试号池",
    "poolType": "测试类型",
    "expiresAt": "2024-12-31T23:59:59.000Z",
    "acquiredAt": "2024-01-01T12:00:00.000Z"
  }
}
```

#### 2. 上报账号状态（归还）
```http
POST /api/v1/account/report
Content-Type: application/json

{
  "accountId": "account-id",
  "result": "ok",
  "notes": "使用成功"
}
```

**请求参数：**
- `accountId`：账号ID（必需）
- `result`：使用结果，`ok` 或 `failed`（必需）
- `notes`：备注信息（可选）

**响应示例：**
```json
{
  "success": true,
  "message": "账号状态已更新"
}
```

#### 3. 获取统计信息
```http
GET /api/v1/stats
GET /api/v1/stats?poolName=池名称
GET /api/v1/stats?poolType=池类型
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "totalPools": 5,
    "totalAccounts": 1000,
    "availableAccounts": 800,
    "inUseAccounts": 150,
    "invalidAccounts": 30,
    "expiredAccounts": 20,
    "pools": [
      {
        "name": "测试号池",
        "type": "测试类型",
        "total": 200,
        "available": 160,
        "inUse": 30,
        "invalid": 6,
        "expired": 4
      }
    ]
  }
}
```

#### 4. 健康检查
```http
GET /api/v1/health
```

**响应示例：**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T12:00:00.000Z",
  "version": "1.0.0"
}
```

### 错误处理

#### 错误响应格式
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "错误描述",
    "details": "详细信息"
  }
}
```

#### 常见错误码
- `INVALID_API_KEY`：API密钥无效
- `POOL_NOT_FOUND`：号池不存在
- `NO_AVAILABLE_ACCOUNT`：没有可用账号
- `ACCOUNT_NOT_FOUND`：账号不存在
- `INVALID_PARAMETERS`：参数无效

### 使用示例

#### JavaScript/Node.js
```javascript
const apiKey = 'your_api_key_here';
const baseUrl = 'https://your-domain.com';

// 获取账号
async function getAccount(poolName) {
  const response = await fetch(`${baseUrl}/api/v1/account?poolName=${poolName}`, {
    headers: {
      'X-API-KEY': apiKey
    }
  });
  
  const data = await response.json();
  if (data.success) {
    return data.data;
  } else {
    throw new Error(data.error.message);
  }
}

// 上报账号状态
async function reportAccount(accountId, result, notes) {
  const response = await fetch(`${baseUrl}/api/v1/account/report`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-API-KEY': apiKey
    },
    body: JSON.stringify({
      accountId,
      result,
      notes
    })
  });
  
  const data = await response.json();
  if (!data.success) {
    throw new Error(data.error.message);
  }
}

// 使用示例
async function useAccount() {
  try {
    // 获取账号
    const account = await getAccount('测试号池');
    console.log('获取到账号:', account.content);
    
    // 使用账号进行业务操作
    // ... 业务逻辑 ...
    
    // 上报使用结果
    await reportAccount(account.id, 'ok', '使用成功');
    console.log('账号状态已上报');
  } catch (error) {
    console.error('操作失败:', error.message);
  }
}
```

#### Python
```python
import requests
import json

class PoolClient:
    def __init__(self, base_url, api_key):
        self.base_url = base_url
        self.headers = {'X-API-KEY': api_key}
    
    def get_account(self, pool_name=None, pool_type=None):
        params = {}
        if pool_name:
            params['poolName'] = pool_name
        elif pool_type:
            params['poolType'] = pool_type
        else:
            raise ValueError('必须指定 pool_name 或 pool_type')
        
        response = requests.get(
            f'{self.base_url}/api/v1/account',
            headers=self.headers,
            params=params
        )
        
        data = response.json()
        if data['success']:
            return data['data']
        else:
            raise Exception(data['error']['message'])
    
    def report_account(self, account_id, result, notes=None):
        payload = {
            'accountId': account_id,
            'result': result
        }
        if notes:
            payload['notes'] = notes
        
        response = requests.post(
            f'{self.base_url}/api/v1/account/report',
            headers={**self.headers, 'Content-Type': 'application/json'},
            json=payload
        )
        
        data = response.json()
        if not data['success']:
            raise Exception(data['error']['message'])

# 使用示例
client = PoolClient('https://your-domain.com', 'your_api_key_here')

try:
    # 获取账号
    account = client.get_account(pool_name='测试号池')
    print(f'获取到账号: {account["content"]}')
    
    # 使用账号进行业务操作
    # ... 业务逻辑 ...
    
    # 上报使用结果
    client.report_account(account['id'], 'ok', '使用成功')
    print('账号状态已上报')
except Exception as e:
    print(f'操作失败: {e}')
```

## 最佳实践

### 1. 号池设计
- **合理分类**：根据业务需求创建不同类型的号池
- **适量规模**：单个号池建议不超过10000个账号
- **定期清理**：及时清理过期和失效的账号

### 2. 账号管理
- **设置过期时间**：为账号设置合理的过期时间
- **状态监控**：定期检查账号状态分布
- **备份重要数据**：定期导出重要账号数据

### 3. API使用
- **错误处理**：实现完善的错误处理机制
- **重试机制**：对网络错误实现自动重试
- **频率控制**：避免过于频繁的API调用

### 4. 安全建议
- **API密钥安全**：妥善保管API密钥，定期更换
- **访问控制**：限制API访问来源
- **日志监控**：监控异常的API调用

### 5. 性能优化
- **批量操作**：优先使用批量导入功能
- **合理筛选**：使用筛选功能减少数据传输
- **缓存策略**：在客户端实现适当的缓存

## 故障排除

### 常见问题

#### Q: 无法获取账号
**可能原因：**
- 号池中没有可用账号
- API密钥无效
- 号池名称或类型错误

**解决方案：**
1. 检查号池中是否有可用账号
2. 验证API密钥是否正确
3. 确认号池名称或类型拼写正确

#### Q: 账号状态异常
**可能原因：**
- 账号已过期
- 账号被标记为失效
- 并发获取导致状态冲突

**解决方案：**
1. 检查账号过期时间
2. 手动重新激活失效账号
3. 实现客户端重试机制

#### Q: 批量导入失败
**可能原因：**
- 文件格式不正确
- 账号内容重复
- 文件过大

**解决方案：**
1. 检查导入格式是否正确
2. 去除重复的账号内容
3. 分批导入大量账号

#### Q: 系统响应慢
**可能原因：**
- 数据量过大
- 网络连接问题
- 服务器资源不足

**解决方案：**
1. 使用分页和筛选功能
2. 检查网络连接状态
3. 联系管理员检查服务器状态

### 联系支持
如果遇到无法解决的问题，请：
1. 查看系统监控页面的健康状态
2. 检查浏览器控制台的错误信息
3. 记录问题发生的具体步骤
4. 联系系统管理员或技术支持

---

更多技术细节请参考 [部署指南](./DEPLOYMENT.md) 和 [API文档](./API.md)。
