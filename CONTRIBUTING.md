# 贡献指南

感谢您对简易号池管理系统的关注！我们欢迎所有形式的贡献，包括但不限于：

- 🐛 报告 Bug
- 💡 提出新功能建议
- 📝 改进文档
- 🔧 提交代码修复
- ✨ 开发新功能

## 开始之前

在开始贡献之前，请：

1. 阅读我们的 [行为准则](#行为准则)
2. 查看 [现有的 Issues](https://github.com/your-username/qnb-pool/issues)
3. 了解项目的 [技术架构](#技术架构)

## 如何贡献

### 报告 Bug

如果您发现了 Bug，请：

1. 检查是否已有相关的 Issue
2. 如果没有，请创建新的 Issue，包含：
   - 清晰的标题和描述
   - 重现步骤
   - 预期行为和实际行为
   - 环境信息（操作系统、浏览器、Node.js 版本等）
   - 相关的错误日志或截图

### 提出功能建议

如果您有新功能的想法：

1. 检查是否已有相关的 Issue 或讨论
2. 创建新的 Feature Request Issue，包含：
   - 功能的详细描述
   - 使用场景和价值
   - 可能的实现方案
   - 相关的设计稿或原型（如有）

### 提交代码

#### 开发环境设置

1. **Fork 项目**
```bash
# 克隆您的 Fork
git clone https://github.com/your-username/qnb-pool.git
cd qnb-pool

# 添加上游仓库
git remote add upstream https://github.com/original-owner/qnb-pool.git
```

2. **安装依赖**
```bash
npm install
```

3. **环境配置**
```bash
cp .env.example .env
# 编辑 .env 文件，配置数据库连接等
```

4. **数据库设置**
```bash
npx prisma generate
npx prisma db push
```

5. **启动开发服务器**
```bash
npm run dev
```

#### 开发流程

1. **创建分支**
```bash
# 从最新的 main 分支创建功能分支
git checkout main
git pull upstream main
git checkout -b feature/your-feature-name
```

2. **开发和测试**
```bash
# 开发您的功能
# 运行测试
npm run test

# 检查代码质量
npm run lint
npm run typecheck
```

3. **提交代码**
```bash
# 添加变更
git add .

# 提交（请遵循提交信息规范）
git commit -m "feat: add new feature description"

# 推送到您的 Fork
git push origin feature/your-feature-name
```

4. **创建 Pull Request**
   - 在 GitHub 上创建 Pull Request
   - 填写详细的描述
   - 关联相关的 Issue
   - 等待代码审查

## 代码规范

### 提交信息规范

我们使用 [Conventional Commits](https://www.conventionalcommits.org/) 规范：

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

**类型 (type):**
- `feat`: 新功能
- `fix`: Bug 修复
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

**示例:**
```
feat(api): add account batch import endpoint
fix(ui): resolve modal close button issue
docs: update deployment guide
```

### 代码风格

- 使用 TypeScript 进行类型检查
- 遵循 ESLint 配置的代码规范
- 使用 Prettier 进行代码格式化
- 组件和函数使用描述性命名
- 添加适当的注释和文档

### 文件结构

```
src/
├── components/          # Vue 组件
│   ├── ui/             # 基础 UI 组件
│   └── business/       # 业务组件
├── pages/              # 页面组件
├── server/             # 服务端 API
├── stores/             # Pinia 状态管理
├── composables/        # 组合式函数
├── utils/              # 工具函数
├── types/              # TypeScript 类型定义
└── assets/             # 静态资源
```

## 测试

### 运行测试

```bash
# 运行所有测试
npm run test

# 运行特定测试文件
npm run test -- tests/api.test.ts

# 运行测试并生成覆盖率报告
npm run test:coverage
```

### 编写测试

- 为新功能编写单元测试
- 为 API 端点编写集成测试
- 确保测试覆盖率不低于 80%
- 使用描述性的测试名称

## 文档

### 更新文档

如果您的更改影响到：
- API 接口：更新 API 文档
- 用户界面：更新使用文档
- 部署配置：更新部署指南
- 新功能：添加相应的文档说明

### 文档风格

- 使用清晰、简洁的语言
- 提供具体的示例
- 包含必要的截图或图表
- 保持文档的时效性

## 代码审查

### 审查标准

我们会从以下方面审查代码：

- **功能性**: 代码是否实现了预期功能
- **质量**: 代码是否清晰、可维护
- **性能**: 是否有性能问题
- **安全性**: 是否存在安全隐患
- **测试**: 是否有足够的测试覆盖
- **文档**: 是否更新了相关文档

### 审查流程

1. 自动化检查（CI/CD）
2. 代码审查（至少一名维护者）
3. 测试验证
4. 合并到主分支

## 发布流程

### 版本管理

- 使用语义化版本号
- 主要版本：不兼容的 API 更改
- 次要版本：向后兼容的新功能
- 补丁版本：向后兼容的 Bug 修复

### 发布步骤

1. 更新版本号
2. 更新 CHANGELOG.md
3. 创建 Release Tag
4. 自动部署到生产环境

## 技术架构

### 前端技术栈
- **框架**: Nuxt 3 + Vue 3
- **语言**: TypeScript
- **样式**: Tailwind CSS
- **状态管理**: Pinia
- **构建工具**: Vite

### 后端技术栈
- **运行时**: Node.js
- **框架**: Nuxt 3 Server API
- **数据库**: MySQL / TiDB Cloud
- **ORM**: Prisma

### 开发工具
- **代码检查**: ESLint + Prettier
- **类型检查**: TypeScript
- **测试框架**: Vitest
- **CI/CD**: GitHub Actions

## 行为准则

### 我们的承诺

为了营造一个开放和友好的环境，我们承诺：

- 尊重不同的观点和经验
- 优雅地接受建设性批评
- 专注于对社区最有利的事情
- 对其他社区成员表示同理心

### 不可接受的行为

- 使用性别化语言或图像，以及不受欢迎的性关注或性骚扰
- 恶意评论、人身攻击或政治攻击
- 公开或私下骚扰
- 未经明确许可发布他人的私人信息
- 在专业环境中可能被认为不合适的其他行为

## 获得帮助

如果您需要帮助或有疑问：

- 📖 查看 [使用文档](./USAGE.md)
- 🚀 查看 [部署指南](./DEPLOYMENT.md)
- 💬 在 [Discussions](https://github.com/your-username/qnb-pool/discussions) 中提问
- 📧 联系维护者

## 致谢

感谢所有为项目做出贡献的开发者！

您的贡献将被记录在 [贡献者列表](https://github.com/your-username/qnb-pool/graphs/contributors) 中。

---

再次感谢您的贡献！🎉
