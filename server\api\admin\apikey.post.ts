/**
 * 重新生成API密钥接口
 * POST /api/admin/apikey
 */

import { prisma, executeTransaction } from '../../utils/db'

export default defineEventHandler(async (event) => {
  try {
    // 使用事务确保操作的原子性
    const result = await executeTransaction(async (tx) => {
      // 1. 删除所有现有的API密钥
      await tx.apiKey.deleteMany({})
      
      // 2. 创建新的API密钥
      const newApiKey = await tx.apiKey.create({
        data: {}
      })
      
      return newApiKey
    })

    console.log(`New API key generated: ${result.key.substring(0, 8)}...`)

    return {
      success: true,
      message: 'API key regenerated successfully',
      data: {
        id: result.id,
        key: result.key,
        createdAt: result.createdAt,
        // 提供部分脱敏的密钥用于显示
        maskedKey: `${result.key.substring(0, 8)}...${result.key.substring(result.key.length - 4)}`
      }
    }
  } catch (error) {
    console.error('Failed to regenerate API key:', error)
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to regenerate API key'
    })
  }
})
