/**
 * 数据库相关类型定义
 */

import type { PrismaClient, AccountStatus } from '@prisma/client'

// 数据库统计信息类型
export interface DatabaseStats {
  poolTypes: number
  pools: number
  accounts: number
  availableAccounts: number
  usedAccounts: number
  invalidAccounts: number
  expiredAccounts: number
  apiKeys: number
  timestamp: string
}

// 号池统计信息类型
export interface PoolStats {
  total: number
  available: number
  inUse: number
  invalid: number
  expired: number
}

// 系统初始化结果类型
export interface SystemInitResult {
  success: boolean
  apiKey?: string
  expiredAccountsCount?: number
  error?: string
}

// 号池类型及统计信息
export interface PoolTypeWithStats {
  id: string
  name: string
  pools: Array<{
    id: string
    name: string
    poolTypeId: string
    _count: {
      accounts: number
    }
  }>
  stats: {
    totalPools: number
    totalAccounts: number
    availableAccounts: number
  }
}

// 事务回调函数类型
export type TransactionCallback<T> = (tx: PrismaClient) => Promise<T>

// API响应状态类型
export type ApiResponseStatus = 'ok' | 'failed'

// 数据库连接状态类型
export interface ConnectionStatus {
  connected: boolean
  error?: string
  timestamp: string
}
