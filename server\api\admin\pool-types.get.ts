/**
 * 获取所有号池类型接口
 * GET /api/admin/pool-types
 */

import { prisma } from '../../utils/db'

export default defineEventHandler(async (event) => {
  try {
    // 获取所有号池类型，包含关联的号池信息和统计数据
    const poolTypes = await prisma.poolType.findMany({
      include: {
        pools: {
          include: {
            _count: {
              select: {
                accounts: true
              }
            }
          }
        }
      },
      orderBy: {
        name: 'asc'
      }
    })

    // 为每个号池类型计算统计信息
    const poolTypesWithStats = await Promise.all(
      poolTypes.map(async (poolType) => {
        let totalAccounts = 0
        let availableAccounts = 0
        let inUseAccounts = 0
        let invalidAccounts = 0
        let expiredAccounts = 0

        // 计算每个号池类型下所有账号的统计信息
        for (const pool of poolType.pools) {
          const accountStats = await prisma.account.groupBy({
            by: ['status'],
            where: {
              poolId: pool.id
            },
            _count: {
              status: true
            }
          })

          accountStats.forEach(stat => {
            totalAccounts += stat._count.status
            switch (stat.status) {
              case 'Available':
                availableAccounts += stat._count.status
                break
              case 'InUse':
                inUseAccounts += stat._count.status
                break
              case 'Invalid':
                invalidAccounts += stat._count.status
                break
              case 'Expired':
                expiredAccounts += stat._count.status
                break
            }
          })
        }

        return {
          id: poolType.id,
          name: poolType.name,
          pools: poolType.pools.map(pool => ({
            id: pool.id,
            name: pool.name,
            accountCount: pool._count.accounts
          })),
          stats: {
            totalPools: poolType.pools.length,
            totalAccounts,
            availableAccounts,
            inUseAccounts,
            invalidAccounts,
            expiredAccounts
          }
        }
      })
    )

    return {
      success: true,
      data: poolTypesWithStats,
      total: poolTypesWithStats.length
    }
  } catch (error) {
    console.error('Failed to get pool types:', error)
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to get pool types'
    })
  }
})
