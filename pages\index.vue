<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 页面头部 -->
    <div class="bg-white shadow-sm border-b">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between h-16">
          <div class="flex items-center">
            <h1 class="text-2xl font-bold text-gray-900">EasyPool</h1>
            <span class="ml-2 text-sm text-gray-500">简易号池管理系统</span>
          </div>

          <div class="flex items-center gap-4">
            <!-- API密钥管理按钮 -->
            <NuxtLink to="/api-keys">
              <UiButton
                size="sm"
                variant="ghost"
              >
                <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1721 9z" />
                </svg>
                API密钥
              </UiButton>
            </NuxtLink>

            <!-- 用户管理按钮 -->
            <NuxtLink to="/admin/users">
              <UiButton
                size="sm"
                variant="ghost"
              >
                <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                </svg>
                用户管理
              </UiButton>
            </NuxtLink>

            <!-- API测试按钮 -->
            <NuxtLink to="/api-test">
              <UiButton
                size="sm"
                variant="ghost"
              >
                <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                API测试
              </UiButton>
            </NuxtLink>

            <!-- 刷新按钮 -->
            <UiButton
              size="sm"
              variant="ghost"
              :loading="refreshing"
              @click="refreshData"
            >
              <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
            </UiButton>

            <!-- 管理员信息和登出 -->
            <div class="flex items-center gap-2">
              <span class="text-sm text-gray-600">管理员</span>
              <UiButton
                size="sm"
                variant="ghost"
                @click="handleLogout"
              >
                <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                </svg>
                登出
              </UiButton>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- 统计概览 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <StatsCard
          title="号池类型"
          :value="stats.poolTypes"
          icon="FolderIcon"
          icon-color="blue"
          :loading="loading"
        />

        <StatsCard
          title="号池总数"
          :value="stats.pools"
          icon="DatabaseIcon"
          icon-color="green"
          :loading="loading"
        />

        <StatsCard
          title="账号总数"
          :value="stats.accounts"
          icon="UserGroupIcon"
          icon-color="purple"
          :loading="loading"
        />

        <StatsCard
          title="可用账号"
          :value="stats.availableAccounts"
          icon="CheckCircleIcon"
          icon-color="green"
          :progress="stats.accounts > 0 ? Math.round((stats.availableAccounts / stats.accounts) * 100) : 0"
          progress-label="可用率"
          progress-color="green"
          :loading="loading"
        />
      </div>

      <!-- 搜索和筛选 -->
      <div class="bg-white shadow rounded-lg mb-6">
        <div class="px-4 py-5 sm:p-6">
          <div class="flex flex-col sm:flex-row gap-4">
            <!-- 搜索框 -->
            <div class="flex-1">
              <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
                <input
                  v-model="searchQuery"
                  type="text"
                  class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                  placeholder="搜索号池名称..."
                />
              </div>
            </div>

            <!-- 类型筛选 -->
            <div class="sm:w-48">
              <select
                v-model="selectedPoolType"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md leading-5 bg-white focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
              >
                <option value="">所有类型</option>
                <option v-for="type in poolTypes" :key="type.id" :value="type.id">
                  {{ type.name }}
                </option>
              </select>
            </div>

            <!-- 操作按钮 -->
            <div class="flex gap-2">
              <UiButton
                variant="primary"
                @click="showCreatePool = true"
              >
                <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                新建号池
              </UiButton>

              <UiButton
                variant="secondary"
                @click="showCreatePoolType = true"
              >
                新建类型
              </UiButton>
            </div>
          </div>
        </div>
      </div>

      <!-- 号池列表 -->
      <div v-if="loading" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div v-for="i in 6" :key="i" class="bg-white rounded-lg shadow animate-pulse">
          <div class="p-6">
            <div class="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
            <div class="h-3 bg-gray-200 rounded w-1/2 mb-4"></div>
            <div class="space-y-2">
              <div class="h-3 bg-gray-200 rounded"></div>
              <div class="h-3 bg-gray-200 rounded w-5/6"></div>
            </div>
          </div>
        </div>
      </div>

      <div v-else-if="filteredPools.length === 0" class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">暂无号池</h3>
        <p class="mt-1 text-sm text-gray-500">开始创建您的第一个号池吧</p>
        <div class="mt-6">
          <UiButton
            variant="primary"
            @click="showCreatePoolModal = true"
          >
            <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            新建号池
          </UiButton>
        </div>
      </div>

      <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <PoolCard
          v-for="pool in filteredPools"
          :key="pool.id"
          :pool="pool"
          @click="navigateToPool"
          @edit="editPool"
          @delete="deletePool"
        />
      </div>
    </div>

    <!-- 模态框占位符 -->
    <div v-if="showSettings" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
        <h3 class="text-lg font-medium text-gray-900 mb-4">系统设置</h3>
        <p class="text-sm text-gray-500 mb-4">设置功能正在开发中...</p>
        <button
          @click="showSettings = false"
          class="w-full bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700"
        >
          关闭
        </button>
      </div>
    </div>

    <div v-if="showCreatePoolType" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
        <h3 class="text-lg font-medium text-gray-900 mb-4">创建号池类型</h3>
        <form @submit.prevent="handleCreatePoolType">
          <div class="mb-4">
            <label for="poolTypeName" class="block text-sm font-medium text-gray-700 mb-2">
              类型名称
            </label>
            <input
              id="poolTypeName"
              v-model="poolTypeForm.name"
              type="text"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
              placeholder="请输入号池类型名称"
            />
          </div>
          <div class="flex gap-3">
            <button
              type="button"
              @click="showCreatePoolType = false"
              class="flex-1 bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400"
            >
              取消
            </button>
            <button
              type="submit"
              :disabled="!poolTypeForm.name.trim() || creating"
              class="flex-1 bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {{ creating ? '创建中...' : '创建' }}
            </button>
          </div>
        </form>
      </div>
    </div>

    <div v-if="showCreatePool" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
        <h3 class="text-lg font-medium text-gray-900 mb-4">创建号池</h3>
        <form @submit.prevent="handleCreatePool">
          <div class="mb-4">
            <label for="poolName" class="block text-sm font-medium text-gray-700 mb-2">
              号池名称
            </label>
            <input
              id="poolName"
              v-model="poolForm.name"
              type="text"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
              placeholder="请输入号池名称"
            />
          </div>
          <div class="mb-4">
            <label for="poolType" class="block text-sm font-medium text-gray-700 mb-2">
              号池类型
            </label>
            <select
              id="poolType"
              v-model="poolForm.poolTypeId"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
            >
              <option value="">请选择号池类型</option>
              <option v-for="type in poolTypes" :key="type.id" :value="type.id">
                {{ type.name }}
              </option>
            </select>
          </div>
          <div class="mb-4">
            <label for="poolDescription" class="block text-sm font-medium text-gray-700 mb-2">
              号池描述
            </label>
            <textarea
              id="poolDescription"
              v-model="poolForm.description"
              rows="3"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 resize-vertical"
              placeholder="请输入号池描述（可选）"
            ></textarea>
          </div>
          <div class="flex gap-3">
            <button
              type="button"
              @click="showCreatePool = false"
              class="flex-1 bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400"
            >
              取消
            </button>
            <button
              type="submit"
              :disabled="!poolForm.name.trim() || !poolForm.poolTypeId || creating"
              class="flex-1 bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {{ creating ? '创建中...' : '创建' }}
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- 编辑号池模态框 -->
    <div v-if="showEditPool" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
        <h3 class="text-lg font-medium text-gray-900 mb-4">编辑号池</h3>
        <form @submit.prevent="handleEditPool">
          <div class="mb-4">
            <label for="editPoolName" class="block text-sm font-medium text-gray-700 mb-2">
              号池名称
            </label>
            <input
              id="editPoolName"
              v-model="editPoolForm.name"
              type="text"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
              placeholder="请输入号池名称"
            />
          </div>
          <div class="mb-4">
            <label for="editPoolType" class="block text-sm font-medium text-gray-700 mb-2">
              号池类型
            </label>
            <select
              id="editPoolType"
              v-model="editPoolForm.poolTypeId"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
            >
              <option value="">请选择号池类型</option>
              <option v-for="type in poolTypes" :key="type.id" :value="type.id">
                {{ type.name }}
              </option>
            </select>
          </div>
          <div class="mb-4">
            <label for="editPoolDescription" class="block text-sm font-medium text-gray-700 mb-2">
              号池描述
            </label>
            <textarea
              id="editPoolDescription"
              v-model="editPoolForm.description"
              rows="3"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 resize-vertical"
              placeholder="请输入号池描述（可选）"
            ></textarea>
          </div>
          <div class="flex gap-3">
            <button
              type="button"
              @click="closeEditPoolModal"
              class="flex-1 bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400"
            >
              取消
            </button>
            <button
              type="submit"
              :disabled="!editPoolForm.name.trim() || !editPoolForm.poolTypeId || editing"
              class="flex-1 bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {{ editing ? '保存中...' : '保存' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">

// 页面标题
useHead({
  title: 'EasyPool - 简易号池管理系统'
})

// 响应式数据
const showSettings = ref(false)
const showCreatePoolType = ref(false)
const showCreatePool = ref(false)
const showEditPool = ref(false)
const showCreatePoolModal = ref(false)
const showPoolTypeModal = ref(false)
const loading = ref(true)
const creating = ref(false)
const refreshing = ref(false)
const editing = ref(false)

// 搜索和筛选
const searchQuery = ref('')
const selectedPoolType = ref('')

// 表单数据
const poolTypeForm = ref({
  name: ''
})

const poolForm = ref({
  name: '',
  description: '',
  poolTypeId: ''
})

const editPoolForm = ref({
  id: '',
  name: '',
  description: '',
  poolTypeId: ''
})

// 数据
const poolTypes = ref([])
const pools = ref([])

// 统计数据
const stats = ref({
  poolTypes: 0,
  pools: 0,
  accounts: 0,
  availableAccounts: 0,
  usedAccounts: 0,
  bannedAccounts: 0
})

// 计算属性
const filteredPools = computed(() => {
  let filtered = pools.value

  // 按名称搜索
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(pool =>
      pool.name.toLowerCase().includes(query)
    )
  }

  // 按类型筛选
  if (selectedPoolType.value) {
    filtered = filtered.filter(pool =>
      pool.poolTypeId === selectedPoolType.value
    )
  }

  return filtered
})

// 加载统计数据
const loadStats = async () => {
  try {
    loading.value = true
    const response = await $fetch<{ success: boolean; data: any }>('/api/v1/health')
    if (response.success && response.data.stats) {
      stats.value = {
        poolTypes: response.data.stats.poolTypes || 0,
        pools: response.data.stats.pools || 0,
        accounts: response.data.stats.accounts || 0,
        availableAccounts: response.data.stats.availableAccounts || 0,
        usedAccounts: response.data.stats.usedAccounts || 0,
        bannedAccounts: response.data.stats.bannedAccounts || 0
      }
    }
  } catch (error) {
    console.error('Failed to load stats:', error)
  } finally {
    loading.value = false
  }
}

// 加载号池类型
const loadPoolTypes = async () => {
  try {
    const response = await $fetch('/api/admin/pool-types')
    if (response.success) {
      poolTypes.value = response.data
    }
  } catch (error) {
    console.error('Failed to load pool types:', error)
  }
}

// 加载号池列表
const loadPools = async () => {
  try {
    const response = await $fetch('/api/admin/pools')
    if (response.success) {
      pools.value = response.data
    }
  } catch (error) {
    console.error('Failed to load pools:', error)
  }
}

// 刷新所有数据
const refreshData = async () => {
  try {
    refreshing.value = true
    await Promise.all([
      loadStats(),
      loadPoolTypes(),
      loadPools()
    ])
  } catch (error) {
    console.error('Failed to refresh data:', error)
  } finally {
    refreshing.value = false
  }
}

// 创建号池类型
const handleCreatePoolType = async () => {
  if (!poolTypeForm.value.name.trim()) return

  try {
    creating.value = true
    const response = await $fetch('/api/admin/pool-types', {
      method: 'POST',
      body: {
        name: poolTypeForm.value.name.trim()
      }
    })

    if (response.success) {
      // 重新加载统计数据和号池类型
      await Promise.all([loadStats(), loadPoolTypes()])
      // 重置表单
      poolTypeForm.value.name = ''
      // 关闭模态框
      showCreatePoolType.value = false
      // 显示成功消息
      console.log('号池类型创建成功:', response.data.name)
    }
  } catch (error) {
    console.error('创建号池类型失败:', error)
  } finally {
    creating.value = false
  }
}

// 创建号池
const handleCreatePool = async () => {
  if (!poolForm.value.name.trim() || !poolForm.value.poolTypeId) return

  try {
    creating.value = true
    const response = await $fetch('/api/admin/pools', {
      method: 'POST',
      body: {
        name: poolForm.value.name.trim(),
        description: poolForm.value.description.trim() || undefined,
        poolTypeId: poolForm.value.poolTypeId
      }
    })

    if (response.success) {
      // 重新加载统计数据和号池列表
      await Promise.all([loadStats(), loadPools()])
      // 重置表单
      poolForm.value.name = ''
      poolForm.value.description = ''
      poolForm.value.poolTypeId = ''
      // 关闭模态框
      showCreatePool.value = false
      // 显示成功消息
      console.log('号池创建成功:', response.data.name)
    }
  } catch (error) {
    console.error('创建号池失败:', error)
  } finally {
    creating.value = false
  }
}

// 号池操作
const navigateToPool = (pool) => {
  navigateTo(`/pools/${pool.id}`)
}

const editPool = (pool) => {
  editPoolForm.value = {
    id: pool.id,
    name: pool.name,
    description: pool.description || '',
    poolTypeId: pool.poolTypeId
  }
  showEditPool.value = true
}

// 编辑号池处理函数
const handleEditPool = async () => {
  if (!editPoolForm.value.name.trim() || !editPoolForm.value.poolTypeId) return

  try {
    editing.value = true
    const response = await $fetch(`/api/admin/pools/${editPoolForm.value.id}`, {
      method: 'PUT',
      body: {
        name: editPoolForm.value.name.trim(),
        description: editPoolForm.value.description.trim() || undefined,
        poolTypeId: editPoolForm.value.poolTypeId
      }
    })

    if (response.success) {
      // 重新加载统计数据和号池列表
      await Promise.all([loadStats(), loadPools()])
      // 关闭模态框
      closeEditPoolModal()
      // 显示成功消息
      console.log('号池编辑成功:', response.data.name)
    }
  } catch (error) {
    console.error('编辑号池失败:', error)
  } finally {
    editing.value = false
  }
}

// 关闭编辑模态框
const closeEditPoolModal = () => {
  showEditPool.value = false
  editPoolForm.value = {
    id: '',
    name: '',
    description: '',
    poolTypeId: ''
  }
}

const deletePool = async (pool) => {
  if (!confirm(`确定要删除号池 "${pool.name}" 吗？`)) return

  try {
    const response = await $fetch(`/api/admin/pools/${pool.id}`, {
      method: 'DELETE'
    })

    if (response.success) {
      await refreshData()
      console.log('号池删除成功')
    }
  } catch (error) {
    console.error('删除号池失败:', error)
  }
}

// 登出处理
const handleLogout = async () => {
  if (!confirm('确定要登出吗？')) return

  try {
    await $fetch('/api/admin/auth/logout', {
      method: 'POST'
    })

    // 重定向到登录页面
    await navigateTo('/admin/login')
  } catch (error) {
    console.error('Logout error:', error)
    // 即使出错也重定向到登录页面
    await navigateTo('/admin/login')
  }
}

// 生命周期
onMounted(() => {
  refreshData()
})
</script>
