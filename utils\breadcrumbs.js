/**
 * 面包屑导航工具函数
 */

/**
 * 根据路由路径生成面包屑导航数据
 * @param {string} path - 当前路由路径
 * @param {Object} params - 路由参数
 * @param {Object} query - 查询参数
 * @returns {Array} 面包屑导航数组
 */
export function generateBreadcrumbs(path, params = {}, query = {}) {
  const breadcrumbs = []

  // 根据不同的路径生成面包屑
  switch (true) {
    // API密钥管理页面
    case path === '/api-keys':
      breadcrumbs.push({ text: 'API密钥管理' })
      break

    // 号池详情页面
    case path.startsWith('/pools/') && params.id:
      breadcrumbs.push(
        { text: '号池管理', to: '/' },
        { text: '号池详情' }
      )
      break

    // 号池类型管理页面
    case path === '/pool-types':
      breadcrumbs.push({ text: '号池类型管理' })
      break

    // 系统设置页面
    case path === '/settings':
      breadcrumbs.push({ text: '系统设置' })
      break

    // 系统监控页面
    case path === '/monitor':
      breadcrumbs.push({ text: '系统监控' })
      break

    // 用户管理页面
    case path === '/users':
      breadcrumbs.push({ text: '用户管理' })
      break

    // 用户详情页面
    case path.startsWith('/users/') && params.id:
      breadcrumbs.push(
        { text: '用户管理', to: '/users' },
        { text: '用户详情' }
      )
      break

    // 日志管理页面
    case path === '/logs':
      breadcrumbs.push({ text: '日志管理' })
      break

    // 统计报表页面
    case path === '/reports':
      breadcrumbs.push({ text: '统计报表' })
      break

    // 帮助文档页面
    case path === '/help':
      breadcrumbs.push({ text: '帮助文档' })
      break

    // 关于页面
    case path === '/about':
      breadcrumbs.push({ text: '关于系统' })
      break

    // 首页或其他页面不显示面包屑
    default:
      return []
  }

  return breadcrumbs
}

/**
 * 根据路由对象生成面包屑导航数据
 * @param {Object} route - Vue Router 路由对象
 * @returns {Array} 面包屑导航数组
 */
export function generateBreadcrumbsFromRoute(route) {
  return generateBreadcrumbs(route.path, route.params, route.query)
}

/**
 * 预定义的页面标题映射
 */
export const PAGE_TITLES = {
  '/': 'EasyPool - 简易号池管理系统',
  '/api-keys': 'API密钥管理 - EasyPool',
  '/pool-types': '号池类型管理 - EasyPool',
  '/settings': '系统设置 - EasyPool',
  '/monitor': '系统监控 - EasyPool',
  '/users': '用户管理 - EasyPool',
  '/logs': '日志管理 - EasyPool',
  '/reports': '统计报表 - EasyPool',
  '/help': '帮助文档 - EasyPool',
  '/about': '关于系统 - EasyPool'
}

/**
 * 根据路径获取页面标题
 * @param {string} path - 路由路径
 * @param {string} defaultTitle - 默认标题
 * @returns {string} 页面标题
 */
export function getPageTitle(path, defaultTitle = 'EasyPool - 简易号池管理系统') {
  // 处理动态路由
  if (path.startsWith('/pools/')) {
    return '号池详情 - EasyPool'
  }
  if (path.startsWith('/users/')) {
    return '用户详情 - EasyPool'
  }

  return PAGE_TITLES[path] || defaultTitle
}
