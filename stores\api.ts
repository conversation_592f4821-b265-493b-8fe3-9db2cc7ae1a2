/**
 * API状态管理
 * 管理API密钥、请求状态等全局API相关状态
 */

import { defineStore } from 'pinia'

export interface ApiKeyInfo {
  id: number
  key: string
  maskedKey: string
  createdAt: string
}

export interface ApiState {
  apiKey: ApiKeyInfo | null
  isLoading: boolean
  error: string | null
  lastUpdated: string | null
}

export const useApiStore = defineStore('api', {
  state: (): ApiState => ({
    apiKey: null,
    isLoading: false,
    error: null,
    lastUpdated: null
  }),

  getters: {
    hasApiKey: (state) => !!state.apiKey,
    isApiKeyValid: (state) => !!state.apiKey?.key,
    apiKeyMasked: (state) => state.apiKey?.maskedKey || '',
    apiKeyCreatedAt: (state) => state.apiKey?.createdAt || null
  },

  actions: {
    /**
     * 获取API密钥
     */
    async fetchApiKey() {
      this.isLoading = true
      this.error = null

      try {
        const { data } = await $fetch<{ success: boolean; data: ApiKeyInfo }>('/api/admin/apikey')
        
        this.apiKey = data
        this.lastUpdated = new Date().toISOString()
        
        return data
      } catch (error: any) {
        this.error = error.message || 'Failed to fetch API key'
        throw error
      } finally {
        this.isLoading = false
      }
    },

    /**
     * 重新生成API密钥
     */
    async regenerateApiKey() {
      this.isLoading = true
      this.error = null

      try {
        const { data } = await $fetch<{ success: boolean; data: ApiKeyInfo }>('/api/admin/apikey', {
          method: 'POST'
        })
        
        this.apiKey = data
        this.lastUpdated = new Date().toISOString()
        
        return data
      } catch (error: any) {
        this.error = error.message || 'Failed to regenerate API key'
        throw error
      } finally {
        this.isLoading = false
      }
    },

    /**
     * 清除API密钥状态
     */
    clearApiKey() {
      this.apiKey = null
      this.error = null
      this.lastUpdated = null
    },

    /**
     * 设置错误状态
     */
    setError(error: string) {
      this.error = error
    },

    /**
     * 清除错误状态
     */
    clearError() {
      this.error = null
    },

    /**
     * 复制API密钥到剪贴板
     */
    async copyApiKey() {
      if (!this.apiKey?.key) {
        throw new Error('No API key available to copy')
      }

      // 检查是否在客户端环境
      if (typeof window === 'undefined') {
        throw new Error('Copy function is only available in browser environment')
      }

      try {
        await navigator.clipboard.writeText(this.apiKey.key)
        return true
      } catch (error) {
        // 如果剪贴板API不可用，使用传统方法
        const textArea = document.createElement('textarea')
        textArea.value = this.apiKey.key
        document.body.appendChild(textArea)
        textArea.select()
        document.execCommand('copy')
        document.body.removeChild(textArea)
        return true
      }
    }
  },

  // TODO: 添加持久化配置
  // persist: {
  //   storage: persistedState.localStorage,
  //   paths: ['apiKey', 'lastUpdated']
  // }
})
