import { z } from 'zod'
import { prisma } from '~/server/utils/db'

// 验证更新状态请求的schema
const updateStatusSchema = z.object({
  status: z.enum(['Active', 'Disabled'], {
    errorMap: () => ({ message: '状态值无效' })
  })
})

export default defineEventHandler(async (event) => {
  try {
    // 获取用户token
    const token = getCookie(event, 'user_token')
    
    if (!token) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Not authenticated'
      })
    }

    // 查找有效的会话
    const session = await prisma.userSession.findUnique({
      where: { token },
      include: {
        user: {
          select: {
            id: true,
            status: true
          }
        }
      }
    })

    if (!session) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Invalid session'
      })
    }

    // 检查会话是否过期
    if (session.expiresAt < new Date()) {
      await prisma.userSession.delete({
        where: { id: session.id }
      })
      
      throw createError({
        statusCode: 401,
        statusMessage: 'Session expired'
      })
    }

    // 检查用户状态
    if (session.user.status !== 'Active') {
      throw createError({
        statusCode: 401,
        statusMessage: 'Account disabled'
      })
    }

    // 获取API密钥ID
    const apiKeyId = getRouterParam(event, 'id')
    if (!apiKeyId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'API key ID is required'
      })
    }

    // 验证请求体
    const body = await readBody(event)
    const { status } = updateStatusSchema.parse(body)

    // 查找API密钥并验证所有权
    const apiKey = await prisma.apiKey.findUnique({
      where: { id: parseInt(apiKeyId) }
    })

    if (!apiKey) {
      throw createError({
        statusCode: 404,
        statusMessage: 'API key not found'
      })
    }

    if (apiKey.userId !== session.user.id) {
      throw createError({
        statusCode: 403,
        statusMessage: 'Access denied'
      })
    }

    // 更新API密钥状态
    await prisma.apiKey.update({
      where: { id: parseInt(apiKeyId) },
      data: { status }
    })

    return {
      success: true,
      message: `API密钥已${status === 'Active' ? '启用' : '禁用'}`
    }

  } catch (error) {
    console.error('Update user API key status error:', error)
    
    if (error.name === 'ZodError') {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid request data',
        data: { message: error.errors[0]?.message || '请求数据无效' }
      })
    }

    if (error.statusCode) {
      throw error
    }

    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to update API key status'
    })
  }
})
