/**
 * 获取API密钥列表接口
 * GET /api/admin/api-keys
 */

import { prisma } from '../../../utils/db'

export default defineEventHandler(async (event) => {
  try {
    // 获取所有API密钥
    const apiKeys = await prisma.apiKey.findMany({
      orderBy: {
        createdAt: 'desc'
      }
    })

    return {
      success: true,
      data: apiKeys
    }
  } catch (error) {
    console.error('Failed to fetch API keys:', error)
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to fetch API keys'
    })
  }
})
