import { prisma } from '~/server/utils/db'

export default defineEventHandler(async (event) => {
  try {
    // 获取用户token
    const token = getCookie(event, 'user_token')
    
    if (!token) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Not authenticated'
      })
    }

    // 查找有效的会话
    const session = await prisma.userSession.findUnique({
      where: { token },
      include: {
        user: {
          select: {
            id: true,
            status: true
          }
        }
      }
    })

    if (!session) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Invalid session'
      })
    }

    // 检查会话是否过期
    if (session.expiresAt < new Date()) {
      await prisma.userSession.delete({
        where: { id: session.id }
      })
      
      throw createError({
        statusCode: 401,
        statusMessage: 'Session expired'
      })
    }

    // 检查用户状态
    if (session.user.status !== 'Active') {
      throw createError({
        statusCode: 401,
        statusMessage: 'Account disabled'
      })
    }

    // 获取API密钥ID
    const apiKeyId = getRouterParam(event, 'id')
    if (!apiKeyId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'API key ID is required'
      })
    }

    // 查找API密钥并验证所有权
    const apiKey = await prisma.apiKey.findUnique({
      where: { id: parseInt(apiKeyId) }
    })

    if (!apiKey) {
      throw createError({
        statusCode: 404,
        statusMessage: 'API key not found'
      })
    }

    if (apiKey.userId !== session.user.id) {
      throw createError({
        statusCode: 403,
        statusMessage: 'Access denied'
      })
    }

    // 删除API密钥
    await prisma.apiKey.delete({
      where: { id: parseInt(apiKeyId) }
    })

    return {
      success: true,
      message: 'API密钥已删除'
    }

  } catch (error) {
    console.error('Delete user API key error:', error)
    
    if (error.statusCode) {
      throw error
    }

    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to delete API key'
    })
  }
})
