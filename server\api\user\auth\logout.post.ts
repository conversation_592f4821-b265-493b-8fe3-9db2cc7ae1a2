import { prisma } from '~/server/utils/db'

export default defineEventHandler(async (event) => {
  try {
    // 获取用户token
    const token = getCookie(event, 'user_token')
    
    if (token) {
      // 删除会话记录
      await prisma.userSession.deleteMany({
        where: { token }
      })
    }

    // 清除<PERSON>ie
    deleteCookie(event, 'user_token', {
      path: '/'
    })

    return {
      success: true,
      message: '登出成功'
    }

  } catch (error) {
    console.error('User logout error:', error)
    
    // 即使出错也要清除<PERSON>ie
    deleteCookie(event, 'user_token', {
      path: '/'
    })

    return {
      success: true,
      message: '登出成功'
    }
  }
})
