<template>
  <div class="overflow-hidden">
    <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200">
        <!-- Header -->
        <thead class="bg-gray-50">
          <tr>
            <th
              v-for="column in columns"
              :key="column.key"
              :class="getHeaderClasses(column)"
              @click="handleSort(column)"
            >
              <div class="flex items-center gap-2">
                <span>{{ column.title }}</span>
                <div v-if="column.sortable" class="flex flex-col">
                  <svg
                    :class="getSortIconClasses(column, 'asc')"
                    class="h-3 w-3"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" />
                  </svg>
                </div>
              </div>
            </th>
          </tr>
        </thead>
        
        <!-- Body -->
        <tbody class="bg-white divide-y divide-gray-200">
          <tr v-if="loading" class="animate-pulse">
            <td
              v-for="column in columns"
              :key="column.key"
              class="px-6 py-4 whitespace-nowrap"
            >
              <div class="h-4 bg-gray-200 rounded"></div>
            </td>
          </tr>
          
          <tr
            v-else-if="data.length === 0"
            class="hover:bg-gray-50"
          >
            <td
              :colspan="columns.length"
              class="px-6 py-12 text-center text-sm text-gray-500"
            >
              <div class="flex flex-col items-center">
                <svg class="h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 009.586 13H7" />
                </svg>
                <p>{{ emptyText || '暂无数据' }}</p>
              </div>
            </td>
          </tr>
          
          <tr
            v-else
            v-for="(row, index) in data"
            :key="getRowKey(row, index)"
            :class="getRowClasses(row, index)"
            @click="handleRowClick(row, index)"
          >
            <td
              v-for="column in columns"
              :key="column.key"
              :class="getCellClasses(column)"
            >
              <slot
                :name="column.key"
                :row="row"
                :column="column"
                :index="index"
                :value="getColumnValue(row, column.key)"
              >
                {{ getColumnValue(row, column.key) }}
              </slot>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Column {
  key: string
  title: string
  sortable?: boolean
  align?: 'left' | 'center' | 'right'
  width?: string
  fixed?: 'left' | 'right'
}

interface Props {
  columns: Column[]
  data: any[]
  loading?: boolean
  emptyText?: string
  rowKey?: string | ((row: any, index: number) => string)
  hoverable?: boolean
  striped?: boolean
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

interface Emits {
  'row-click': [row: any, index: number]
  'sort-change': [column: Column, order: 'asc' | 'desc']
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  hoverable: true,
  striped: false,
  sortOrder: 'asc'
})

const emit = defineEmits<Emits>()

const getHeaderClasses = (column: Column) => {
  const baseClasses = [
    'px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'
  ]
  
  if (column.sortable) {
    baseClasses.push('cursor-pointer hover:bg-gray-100 select-none')
  }
  
  const alignClasses = {
    left: 'text-left',
    center: 'text-center',
    right: 'text-right'
  }
  
  if (column.align && column.align !== 'left') {
    baseClasses.push(alignClasses[column.align])
  }
  
  return baseClasses.join(' ')
}

const getCellClasses = (column: Column) => {
  const baseClasses = ['px-6 py-4 whitespace-nowrap text-sm']
  
  const alignClasses = {
    left: 'text-left',
    center: 'text-center',
    right: 'text-right'
  }
  
  if (column.align) {
    baseClasses.push(alignClasses[column.align])
  }
  
  return baseClasses.join(' ')
}

const getRowClasses = (row: any, index: number) => {
  const classes = []
  
  if (props.hoverable) {
    classes.push('hover:bg-gray-50 cursor-pointer')
  }
  
  if (props.striped && index % 2 === 1) {
    classes.push('bg-gray-50')
  }
  
  return classes.join(' ')
}

const getSortIconClasses = (column: Column, direction: 'asc' | 'desc') => {
  const baseClasses = ['transition-colors duration-200']
  
  if (props.sortBy === column.key && props.sortOrder === direction) {
    baseClasses.push('text-indigo-600')
  } else {
    baseClasses.push('text-gray-400')
  }
  
  if (direction === 'desc') {
    baseClasses.push('rotate-180')
  }
  
  return baseClasses.join(' ')
}

const getRowKey = (row: any, index: number) => {
  if (typeof props.rowKey === 'function') {
    return props.rowKey(row, index)
  } else if (typeof props.rowKey === 'string') {
    return row[props.rowKey]
  } else {
    return index
  }
}

const getColumnValue = (row: any, key: string) => {
  return key.split('.').reduce((obj, k) => obj?.[k], row)
}

const handleSort = (column: Column) => {
  if (!column.sortable) return
  
  let newOrder: 'asc' | 'desc' = 'asc'
  
  if (props.sortBy === column.key) {
    newOrder = props.sortOrder === 'asc' ? 'desc' : 'asc'
  }
  
  emit('sort-change', column, newOrder)
}

const handleRowClick = (row: any, index: number) => {
  emit('row-click', row, index)
}
</script>
