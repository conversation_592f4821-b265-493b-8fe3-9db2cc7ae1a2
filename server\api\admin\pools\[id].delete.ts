/**
 * 删除号池接口
 * DELETE /api/admin/pools/[id]
 */

import { prisma, executeTransaction } from '../../../utils/db'

export default defineEventHandler(async (event) => {
  try {
    // 获取路由参数中的ID
    const id = getRouterParam(event, 'id')
    
    if (!id) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Pool ID is required'
      })
    }

    // 验证ID格式
    if (typeof id !== 'string' || id.length < 10) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid pool ID format'
      })
    }

    // 使用事务确保删除操作的原子性
    const result = await executeTransaction(async (tx) => {
      // 1. 检查号池是否存在
      const pool = await tx.pool.findUnique({
        where: { id },
        include: {
          poolType: true,
          _count: {
            select: {
              accounts: true
            }
          }
        }
      })

      if (!pool) {
        throw createError({
          statusCode: 404,
          statusMessage: 'Pool not found'
        })
      }

      // 2. 获取将要删除的账号统计信息
      const accountStats = await tx.account.groupBy({
        by: ['status'],
        where: {
          poolId: pool.id
        },
        _count: {
          status: true
        }
      })

      const deletionStats = {
        total: 0,
        available: 0,
        inUse: 0,
        invalid: 0,
        expired: 0
      }

      accountStats.forEach(stat => {
        deletionStats.total += stat._count.status
        switch (stat.status) {
          case 'Available':
            deletionStats.available = stat._count.status
            break
          case 'InUse':
            deletionStats.inUse = stat._count.status
            break
          case 'Invalid':
            deletionStats.invalid = stat._count.status
            break
          case 'Expired':
            deletionStats.expired = stat._count.status
            break
        }
      })

      // 3. 删除号池（级联删除会自动删除关联的账号）
      await tx.pool.delete({
        where: { id }
      })

      console.log(`Pool deleted: ${pool.name} (ID: ${id})`)
      console.log(`Cascaded deletion: ${deletionStats.total} accounts`)

      return {
        deletedPool: {
          id: pool.id,
          name: pool.name,
          poolType: {
            id: pool.poolType.id,
            name: pool.poolType.name
          }
        },
        cascadedDeletion: {
          accounts: deletionStats
        }
      }
    })

    return {
      success: true,
      message: 'Pool deleted successfully',
      data: result
    }
  } catch (error) {
    console.error('Failed to delete pool:', error)
    
    // 如果是已知的错误（如验证错误），直接抛出
    if (error.statusCode) {
      throw error
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to delete pool'
    })
  }
})
