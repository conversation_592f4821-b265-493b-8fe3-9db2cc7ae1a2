/**
 * 批量删除账号接口
 * POST /api/admin/accounts/batch-delete
 */

import { prisma } from '../../../utils/db'

export default defineEventHandler(async (event) => {
  try {
    // 获取请求体
    const body = await readBody(event)
    const { accountIds } = body

    // 验证必填字段
    if (!accountIds || !Array.isArray(accountIds) || accountIds.length === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Account IDs array is required and cannot be empty'
      })
    }

    // 验证账号ID格式
    for (const id of accountIds) {
      if (!id || typeof id !== 'string' || id.length < 10) {
        throw createError({
          statusCode: 400,
          statusMessage: 'Invalid account ID format'
        })
      }
    }

    // 检查账号是否存在
    const existingAccounts = await prisma.account.findMany({
      where: {
        id: {
          in: accountIds
        }
      },
      select: {
        id: true,
        content: true,
        status: true
      }
    })

    if (existingAccounts.length !== accountIds.length) {
      const foundIds = existingAccounts.map(acc => acc.id)
      const notFoundIds = accountIds.filter(id => !foundIds.includes(id))
      throw createError({
        statusCode: 404,
        statusMessage: `Some accounts not found: ${notFoundIds.join(', ')}`
      })
    }

    // 检查是否有正在使用的账号
    const inUseAccounts = existingAccounts.filter(acc => acc.status === 'InUse')
    if (inUseAccounts.length > 0) {
      throw createError({
        statusCode: 400,
        statusMessage: `Cannot delete accounts that are in use: ${inUseAccounts.map(acc => acc.content).join(', ')}`
      })
    }

    // 批量删除账号
    const deleteResult = await prisma.account.deleteMany({
      where: {
        id: {
          in: accountIds
        }
      }
    })

    return {
      success: true,
      message: `Successfully deleted ${deleteResult.count} accounts`,
      data: {
        deletedCount: deleteResult.count,
        deletedIds: accountIds
      }
    }
  } catch (error) {
    console.error('Failed to batch delete accounts:', error)
    
    // 如果是已知的错误（如验证错误），直接抛出
    if (error.statusCode) {
      throw error
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to batch delete accounts'
    })
  }
})
