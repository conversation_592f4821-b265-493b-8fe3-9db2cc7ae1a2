/**
 * 删除号池类型接口
 * DELETE /api/admin/pool-types/[id]
 */

import { prisma, executeTransaction } from '../../../utils/db'

export default defineEventHandler(async (event) => {
  try {
    // 获取路由参数中的ID
    const id = getRouterParam(event, 'id')
    
    if (!id) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Pool type ID is required'
      })
    }

    // 验证ID格式（cuid格式）
    if (typeof id !== 'string' || id.length < 10) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid pool type ID format'
      })
    }

    // 使用事务确保删除操作的原子性
    const result = await executeTransaction(async (tx) => {
      // 1. 检查号池类型是否存在
      const poolType = await tx.poolType.findUnique({
        where: { id },
        include: {
          pools: {
            include: {
              _count: {
                select: {
                  accounts: true
                }
              }
            }
          }
        }
      })

      if (!poolType) {
        throw createError({
          statusCode: 404,
          statusMessage: 'Pool type not found'
        })
      }

      // 2. 计算将要删除的统计信息
      let totalPools = poolType.pools.length
      let totalAccounts = 0
      
      poolType.pools.forEach(pool => {
        totalAccounts += pool._count.accounts
      })

      // 3. 删除号池类型（级联删除会自动删除关联的号池和账号）
      await tx.poolType.delete({
        where: { id }
      })

      console.log(`Pool type deleted: ${poolType.name} (ID: ${id})`)
      console.log(`Cascaded deletion: ${totalPools} pools, ${totalAccounts} accounts`)

      return {
        deletedPoolType: {
          id: poolType.id,
          name: poolType.name
        },
        cascadedDeletion: {
          pools: totalPools,
          accounts: totalAccounts
        }
      }
    })

    return {
      success: true,
      message: 'Pool type deleted successfully',
      data: result
    }
  } catch (error) {
    console.error('Failed to delete pool type:', error)
    
    // 如果是已知的错误（如验证错误），直接抛出
    if (error.statusCode) {
      throw error
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to delete pool type'
    })
  }
})
