import { prisma } from '~/server/utils/db'

export default defineEventHandler(async (event) => {
  try {
    // 获取用户token
    const token = getCookie(event, 'user_token')
    
    if (!token) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Not authenticated'
      })
    }

    // 查找有效的会话
    const session = await prisma.userSession.findUnique({
      where: { token },
      include: {
        user: {
          select: {
            id: true,
            status: true
          }
        }
      }
    })

    if (!session) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Invalid session'
      })
    }

    // 检查会话是否过期
    if (session.expiresAt < new Date()) {
      await prisma.userSession.delete({
        where: { id: session.id }
      })
      
      throw createError({
        statusCode: 401,
        statusMessage: 'Session expired'
      })
    }

    // 检查用户状态
    if (session.user.status !== 'Active') {
      throw createError({
        statusCode: 401,
        statusMessage: 'Account disabled'
      })
    }

    // 获取用户的API密钥列表
    const apiKeys = await prisma.apiKey.findMany({
      where: { userId: session.user.id },
      select: {
        id: true,
        name: true,
        key: true,
        description: true,
        status: true,
        permissions: true,
        expiresAt: true,
        createdAt: true,
        updatedAt: true,
        lastUsedAt: true
      },
      orderBy: { createdAt: 'desc' }
    })

    return {
      success: true,
      data: apiKeys
    }

  } catch (error) {
    console.error('Get user API keys error:', error)
    
    if (error.statusCode) {
      throw error
    }

    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to get API keys'
    })
  }
})
