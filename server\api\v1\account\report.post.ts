/**
 * 账号状态上报接口（归还）
 * POST /api/v1/account/report
 * 
 * 用于上报账号使用结果，将账号状态从InUse更新为Available或Invalid
 */

import { prisma } from '../../../utils/db'
import { requireApiKey } from '../../../utils/auth'

export default defineEventHandler(async (event) => {
  try {
    // 1. API密钥认证
    await requireApiKey(event)

    // 2. 获取请求体数据
    const body = await readBody(event)
    
    // 3. 数据验证
    if (!body || !body.accountId || !body.result) {
      throw createError({
        statusCode: 400,
        statusMessage: 'accountId and result are required'
      })
    }

    const { accountId, result, notes } = body

    // 验证accountId格式
    if (typeof accountId !== 'string' || accountId.length < 10) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid account ID format'
      })
    }

    // 验证result值
    const validResults = ['ok', 'failed']
    if (!validResults.includes(result)) {
      throw createError({
        statusCode: 400,
        statusMessage: `Invalid result. Must be one of: ${validResults.join(', ')}`
      })
    }

    // 验证备注长度（如果提供）
    if (notes && typeof notes === 'string' && notes.length > 1000) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Notes must be 1000 characters or less'
      })
    }

    // 4. 查找账号
    const account = await prisma.account.findUnique({
      where: { id: accountId },
      include: {
        pool: {
          include: {
            poolType: true
          }
        }
      }
    })

    if (!account) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Account not found'
      })
    }

    // 5. 检查账号当前状态
    if (account.status !== 'InUse') {
      throw createError({
        statusCode: 400,
        statusMessage: `Account is not in use. Current status: ${account.status}`
      })
    }

    // 6. 确定新状态
    const now = new Date()
    let newStatus: 'Available' | 'Invalid' | 'Expired'

    // 首先检查是否过期
    if (account.expiresAt && account.expiresAt <= now) {
      newStatus = 'Expired'
    } else {
      // 根据使用结果确定状态
      newStatus = result === 'ok' ? 'Available' : 'Invalid'
    }

    // 7. 更新账号状态并清除占用标识
    const updatedAccount = await prisma.account.update({
      where: { id: accountId },
      data: {
        status: newStatus,
        occupiedBy: null, // 清除账号占用标识
        lastUsedAt: now,
        ...(notes !== undefined && { notes: notes || null })
      }
    })

    // 8. 记录归还日志
    console.log(`Account reported: ${accountId} from pool: ${account.pool.name}, result: ${result}, new status: ${newStatus}, occupiedBy cleared: ${account.occupiedBy || 'none'}`)

    // 9. 返回结果
    return {
      success: true,
      message: 'Account status reported successfully',
      data: {
        accountId: updatedAccount.id,
        previousStatus: account.status,
        newStatus: updatedAccount.status,
        result,
        poolName: account.pool.name,
        poolType: account.pool.poolType.name,
        reportedAt: updatedAccount.lastUsedAt,
        wasExpired: newStatus === 'Expired',
        notes: updatedAccount.notes
      }
    }
  } catch (error) {
    console.error('Failed to report account status:', error)
    
    // 如果是已知的错误（如验证错误），直接抛出
    if (error.statusCode) {
      throw error
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to report account status'
    })
  }
})
