/**
 * 批量修改API密钥状态接口
 * PUT /api/admin/api-keys/batch-status
 */

import { prisma } from '../../../utils/db'

export default defineEventHandler(async (event) => {
  try {
    // 获取请求体
    const body = await readBody(event)
    const { apiKeyIds, status } = body

    // 验证必填字段
    if (!apiKeyIds || !Array.isArray(apiKeyIds) || apiKeyIds.length === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: 'API key IDs array is required and cannot be empty'
      })
    }

    if (!status || typeof status !== 'string') {
      throw createError({
        statusCode: 400,
        statusMessage: 'Status is required'
      })
    }

    // 验证状态值
    const validStatuses = ['Active', 'Disabled']
    if (!validStatuses.includes(status)) {
      throw createError({
        statusCode: 400,
        statusMessage: `Invalid status. Must be one of: ${validStatuses.join(', ')}`
      })
    }

    // 验证API密钥ID格式
    for (const id of apiKeyIds) {
      const numId = parseInt(id)
      if (isNaN(numId)) {
        throw createError({
          statusCode: 400,
          statusMessage: 'Invalid API key ID format'
        })
      }
    }

    // 转换为数字数组
    const numericIds = apiKeyIds.map(id => parseInt(id))

    // 检查API密钥是否存在
    const existingApiKeys = await prisma.apiKey.findMany({
      where: {
        id: {
          in: numericIds
        }
      },
      select: {
        id: true,
        name: true,
        status: true,
        expiresAt: true
      }
    })

    if (existingApiKeys.length !== numericIds.length) {
      const foundIds = existingApiKeys.map(key => key.id)
      const notFoundIds = numericIds.filter(id => !foundIds.includes(id))
      throw createError({
        statusCode: 404,
        statusMessage: `Some API keys not found: ${notFoundIds.join(', ')}`
      })
    }

    // 检查是否有过期的API密钥要激活
    if (status === 'Active') {
      const now = new Date()
      const expiredKeys = existingApiKeys.filter(key => 
        key.expiresAt && key.expiresAt <= now
      )
      
      if (expiredKeys.length > 0) {
        throw createError({
          statusCode: 400,
          statusMessage: `Cannot activate expired API keys: ${expiredKeys.map(key => key.name).join(', ')}`
        })
      }
    }

    // 批量更新API密钥状态
    const updateResult = await prisma.apiKey.updateMany({
      where: {
        id: {
          in: numericIds
        }
      },
      data: {
        status: status,
        updatedAt: new Date()
      }
    })

    return {
      success: true,
      message: `Successfully updated ${updateResult.count} API keys to ${status}`,
      data: {
        updatedCount: updateResult.count,
        updatedIds: numericIds,
        newStatus: status
      }
    }
  } catch (error) {
    console.error('Failed to batch update API key status:', error)
    
    // 如果是已知的错误（如验证错误），直接抛出
    if (error.statusCode) {
      throw error
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to batch update API key status'
    })
  }
})
