<template>
  <div class="min-h-screen bg-gray-50 flex items-center justify-center px-4">
    <div class="max-w-md w-full">
      <!-- 错误图标 -->
      <div class="text-center mb-8">
        <div class="inline-flex items-center justify-center w-16 h-16 bg-red-100 rounded-full mb-4">
          <svg class="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        
        <h1 class="text-2xl font-bold text-gray-900 mb-2">
          {{ getErrorTitle() }}
        </h1>
        
        <p class="text-gray-600">
          {{ getErrorMessage() }}
        </p>
      </div>

      <!-- 错误详情 -->
      <div v-if="error.statusCode !== 404" class="bg-white rounded-lg shadow-sm border p-6 mb-6">
        <h3 class="text-sm font-medium text-gray-900 mb-2">错误详情</h3>
        <div class="text-sm text-gray-600 space-y-1">
          <div><strong>状态码:</strong> {{ error.statusCode }}</div>
          <div v-if="error.statusMessage"><strong>状态:</strong> {{ error.statusMessage }}</div>
          <div v-if="error.message"><strong>消息:</strong> {{ error.message }}</div>
          <div v-if="error.url"><strong>路径:</strong> {{ error.url }}</div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="space-y-3">
        <UiButton
          block
          @click="handleError"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
          </svg>
          返回首页
        </UiButton>
        
        <UiButton
          variant="secondary"
          block
          @click="retry"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
          重试
        </UiButton>
        
        <UiButton
          variant="ghost"
          block
          @click="goBack"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
          </svg>
          返回上一页
        </UiButton>
      </div>

      <!-- 帮助信息 -->
      <div class="mt-8 text-center">
        <p class="text-sm text-gray-500">
          如果问题持续存在，请联系系统管理员
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface ErrorProps {
  error: {
    statusCode: number
    statusMessage?: string
    message?: string
    url?: string
  }
}

const props = defineProps<ErrorProps>()

// 页面标题
useHead({
  title: computed(() => `错误 ${props.error.statusCode} - EasyPool`)
})

// 获取错误标题
const getErrorTitle = () => {
  switch (props.error.statusCode) {
    case 404:
      return '页面未找到'
    case 403:
      return '访问被拒绝'
    case 500:
      return '服务器错误'
    case 503:
      return '服务不可用'
    default:
      return '发生错误'
  }
}

// 获取错误消息
const getErrorMessage = () => {
  switch (props.error.statusCode) {
    case 404:
      return '抱歉，您访问的页面不存在或已被移除。'
    case 403:
      return '您没有权限访问此页面。'
    case 500:
      return '服务器内部错误，请稍后重试。'
    case 503:
      return '服务暂时不可用，请稍后重试。'
    default:
      return props.error.message || '发生了未知错误，请稍后重试。'
  }
}

// 处理错误
const handleError = async () => {
  await clearError({ redirect: '/' })
}

// 重试
const retry = () => {
  window.location.reload()
}

// 返回上一页
const goBack = () => {
  if (window.history.length > 1) {
    window.history.back()
  } else {
    navigateTo('/')
  }
}

// 记录错误
onMounted(() => {
  // 在生产环境中，可以将错误发送到错误监控服务
  if (process.env.NODE_ENV === 'production') {
    console.error('Page Error:', {
      statusCode: props.error.statusCode,
      statusMessage: props.error.statusMessage,
      message: props.error.message,
      url: props.error.url,
      userAgent: navigator.userAgent,
      timestamp: new Date().toISOString()
    })
  }
})
</script>
