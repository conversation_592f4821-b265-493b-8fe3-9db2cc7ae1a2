/**
 * 管理员登录接口
 * POST /api/admin/auth/login
 */

import { loginAdmin, createAdminSession, createDefaultAdmin } from '../../../utils/auth'
import type { LoginRequest, LoginResponse } from '~/types/auth'

export default defineEventHandler(async (event) => {
  try {
    // 确保默认管理员存在
    await createDefaultAdmin()

    // 获取请求体
    const body = await readBody(event) as LoginRequest
    
    // 验证请求数据
    if (!body.username || !body.password) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Username and password are required'
      })
    }

    // 验证用户名和密码
    const admin = await loginAdmin(body.username, body.password)
    
    if (!admin) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Invalid username or password'
      })
    }

    // 创建会话
    const session = await createAdminSession(admin.id)

    // 设置HttpOnly <PERSON>ie
    setCookie(event, 'admin_token', session.token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 24 * 60 * 60, // 24小时
      path: '/'
    })

    const response: LoginResponse = {
      success: true,
      admin: session.admin,
      message: 'Login successful'
    }

    return response
  } catch (error) {
    console.error('Login error:', error)
    
    if (error.statusCode) {
      throw error
    }

    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error'
    })
  }
})
