/**
 * 更新账号信息接口
 * PUT /api/admin/accounts/[id]
 */

import { prisma } from '../../../utils/db'

export default defineEventHandler(async (event) => {
  try {
    // 获取路由参数中的ID
    const accountId = getRouterParam(event, 'id')
    
    if (!accountId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Account ID is required'
      })
    }

    // 验证ID格式
    if (typeof accountId !== 'string' || accountId.length < 10) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid account ID format'
      })
    }

    // 获取请求体
    const body = await readBody(event)
    const { content, notes, expiresAt } = body

    // 验证必填字段
    if (!content || typeof content !== 'string' || content.trim().length === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Account content is required'
      })
    }

    // 检查账号是否存在
    const existingAccount = await prisma.account.findUnique({
      where: { id: accountId },
      include: {
        pool: {
          include: {
            poolType: true
          }
        }
      }
    })

    if (!existingAccount) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Account not found'
      })
    }

    // 检查同一号池中是否已存在相同内容的账号（排除当前账号）
    const duplicateAccount = await prisma.account.findFirst({
      where: {
        poolId: existingAccount.poolId,
        content: content.trim(),
        id: {
          not: accountId
        }
      }
    })

    if (duplicateAccount) {
      throw createError({
        statusCode: 409,
        statusMessage: 'Account with this content already exists in the pool'
      })
    }

    // 处理过期时间
    let expiresAtDate = null
    if (expiresAt !== undefined) {
      if (expiresAt) {
        expiresAtDate = new Date(expiresAt)
        if (isNaN(expiresAtDate.getTime())) {
          throw createError({
            statusCode: 400,
            statusMessage: 'Invalid expires date format'
          })
        }
      }
    } else {
      // 如果没有提供 expiresAt，保持原值
      expiresAtDate = existingAccount.expiresAt
    }

    // 更新账号信息
    const updatedAccount = await prisma.account.update({
      where: { id: accountId },
      data: {
        content: content.trim(),
        notes: notes ? notes.trim() : null,
        expiresAt: expiresAtDate,
        updatedAt: new Date()
      },
      include: {
        pool: {
          include: {
            poolType: true
          }
        }
      }
    })

    return {
      success: true,
      data: {
        id: updatedAccount.id,
        content: updatedAccount.content,
        status: updatedAccount.status,
        notes: updatedAccount.notes,
        expiresAt: updatedAccount.expiresAt,
        createdAt: updatedAccount.createdAt,
        updatedAt: updatedAccount.updatedAt,
        lastUsedAt: updatedAccount.lastUsedAt,
        pool: {
          id: updatedAccount.pool.id,
          name: updatedAccount.pool.name,
          poolType: {
            id: updatedAccount.pool.poolType.id,
            name: updatedAccount.pool.poolType.name
          }
        }
      }
    }
  } catch (error) {
    console.error('Failed to update account:', error)
    
    // 如果是已知的错误（如验证错误），直接抛出
    if (error.statusCode) {
      throw error
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to update account'
    })
  }
})
