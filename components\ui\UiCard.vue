<template>
  <div :class="cardClasses" @click="handleClick">
    <!-- Header -->
    <div v-if="title || subtitle || $slots.header || $slots.actions" :class="headerClasses">
      <div class="flex-1">
        <slot name="header">
          <div v-if="title || subtitle">
            <h3 v-if="title" class="text-lg font-semibold text-gray-900">{{ title }}</h3>
            <p v-if="subtitle" class="mt-1 text-sm text-gray-600">{{ subtitle }}</p>
          </div>
        </slot>
      </div>
      
      <div v-if="$slots.actions" class="flex items-center gap-2">
        <slot name="actions" />
      </div>
    </div>
    
    <!-- Body -->
    <div :class="bodyClasses">
      <slot />
    </div>
    
    <!-- Footer -->
    <div v-if="$slots.footer" :class="footerClasses">
      <slot name="footer" />
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  title?: string
  subtitle?: string
  variant?: 'default' | 'outlined' | 'elevated'
  padding?: 'none' | 'sm' | 'md' | 'lg'
  hoverable?: boolean
  clickable?: boolean
}

interface Emits {
  click: [event: MouseEvent]
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'default',
  padding: 'md',
  hoverable: false,
  clickable: false
})

const emit = defineEmits<Emits>()

const cardClasses = computed(() => {
  const baseClasses = ['bg-white rounded-lg transition-all duration-200']
  
  // Variant classes
  const variantClasses = {
    default: 'border border-gray-200',
    outlined: 'border-2 border-gray-300',
    elevated: 'shadow-lg border border-gray-100'
  }
  
  const classes = [
    ...baseClasses,
    variantClasses[props.variant]
  ]
  
  // Interactive states
  if (props.hoverable || props.clickable) {
    classes.push('hover:shadow-md')
  }
  
  if (props.clickable) {
    classes.push('cursor-pointer hover:border-gray-300')
  }
  
  return classes.join(' ')
})

const headerClasses = computed(() => {
  const baseClasses = ['flex items-start justify-between']
  
  const paddingClasses = {
    none: '',
    sm: 'p-4 pb-0',
    md: 'p-6 pb-0',
    lg: 'p-8 pb-0'
  }
  
  return [
    ...baseClasses,
    paddingClasses[props.padding]
  ].join(' ')
})

const bodyClasses = computed(() => {
  const paddingClasses = {
    none: '',
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8'
  }
  
  // Adjust top padding if there's a header
  const hasHeader = props.title || props.subtitle
  let topPadding = ''
  
  if (hasHeader && props.padding !== 'none') {
    const topPaddingClasses = {
      sm: 'pt-2',
      md: 'pt-4',
      lg: 'pt-6'
    }
    topPadding = topPaddingClasses[props.padding]
  }
  
  return [
    paddingClasses[props.padding],
    topPadding
  ].filter(Boolean).join(' ')
})

const footerClasses = computed(() => {
  const baseClasses = ['border-t border-gray-200']
  
  const paddingClasses = {
    none: '',
    sm: 'p-4 pt-4',
    md: 'p-6 pt-4',
    lg: 'p-8 pt-6'
  }
  
  return [
    ...baseClasses,
    paddingClasses[props.padding]
  ].join(' ')
})

const handleClick = (event: MouseEvent) => {
  if (props.clickable) {
    emit('click', event)
  }
}
</script>
