/**
 * 批量修改账号状态接口
 * PUT /api/admin/accounts/batch-status
 */

import { prisma } from '../../../utils/db'

export default defineEventHandler(async (event) => {
  try {
    // 获取请求体
    const body = await readBody(event)
    const { accountIds, status } = body

    // 验证必填字段
    if (!accountIds || !Array.isArray(accountIds) || accountIds.length === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Account IDs array is required and cannot be empty'
      })
    }

    if (!status || typeof status !== 'string') {
      throw createError({
        statusCode: 400,
        statusMessage: 'Status is required'
      })
    }

    // 验证状态值
    const validStatuses = ['Available', 'InUse', 'Invalid', 'Expired']
    if (!validStatuses.includes(status)) {
      throw createError({
        statusCode: 400,
        statusMessage: `Invalid status. Must be one of: ${validStatuses.join(', ')}`
      })
    }

    // 验证账号ID格式
    for (const id of accountIds) {
      if (!id || typeof id !== 'string' || id.length < 10) {
        throw createError({
          statusCode: 400,
          statusMessage: 'Invalid account ID format'
        })
      }
    }

    // 检查账号是否存在
    const existingAccounts = await prisma.account.findMany({
      where: {
        id: {
          in: accountIds
        }
      },
      select: {
        id: true,
        content: true,
        status: true
      }
    })

    if (existingAccounts.length !== accountIds.length) {
      const foundIds = existingAccounts.map(acc => acc.id)
      const notFoundIds = accountIds.filter(id => !foundIds.includes(id))
      throw createError({
        statusCode: 404,
        statusMessage: `Some accounts not found: ${notFoundIds.join(', ')}`
      })
    }

    // 批量更新账号状态
    const updateResult = await prisma.account.updateMany({
      where: {
        id: {
          in: accountIds
        }
      },
      data: {
        status: status,
        updatedAt: new Date()
      }
    })

    return {
      success: true,
      message: `Successfully updated ${updateResult.count} accounts to ${status}`,
      data: {
        updatedCount: updateResult.count,
        updatedIds: accountIds,
        newStatus: status
      }
    }
  } catch (error) {
    console.error('Failed to batch update account status:', error)
    
    // 如果是已知的错误（如验证错误），直接抛出
    if (error.statusCode) {
      throw error
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to batch update account status'
    })
  }
})
