<template>
  <UiCard title="API密钥管理" subtitle="管理系统API访问密钥">
    <div class="space-y-6">
      <!-- API密钥显示 -->
      <div v-if="apiStore.hasApiKey" class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            当前API密钥
          </label>
          <div class="flex items-center gap-3">
            <div class="flex-1">
              <UiInput
                :model-value="showFullKey ? apiStore.apiKey?.key : apiStore.apiKeyMasked"
                readonly
                size="sm"
              >
                <template #suffix>
                  <div class="flex items-center gap-1">
                    <!-- 显示/隐藏按钮 -->
                    <button
                      type="button"
                      class="text-gray-400 hover:text-gray-600 focus:outline-none"
                      @click="toggleKeyVisibility"
                    >
                      <svg v-if="showFullKey" class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                      </svg>
                      <svg v-else class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                    </button>
                    
                    <!-- 复制按钮 -->
                    <button
                      type="button"
                      class="text-gray-400 hover:text-gray-600 focus:outline-none"
                      @click="copyApiKey"
                    >
                      <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                      </svg>
                    </button>
                  </div>
                </template>
              </UiInput>
            </div>
          </div>
        </div>

        <!-- API密钥信息 -->
        <div class="bg-gray-50 rounded-lg p-4">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <span class="font-medium text-gray-700">创建时间：</span>
              <span class="text-gray-600">
                {{ formatDate(apiStore.apiKeyCreatedAt, 'datetime') }}
              </span>
            </div>
            <div>
              <span class="font-medium text-gray-700">最后更新：</span>
              <span class="text-gray-600">
                {{ formatDate(apiStore.lastUpdated, 'relative') }}
              </span>
            </div>
          </div>
        </div>

        <!-- 使用说明 -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h4 class="text-sm font-medium text-blue-900 mb-2">使用说明</h4>
          <div class="text-sm text-blue-800 space-y-1">
            <p>• 在API请求头中添加：<code class="bg-blue-100 px-1 rounded">X-API-KEY: your_api_key</code></p>
            <p>• API基础地址：<code class="bg-blue-100 px-1 rounded">{{ baseUrl }}/api/v1</code></p>
            <p>• 请妥善保管API密钥，不要在客户端代码中暴露</p>
          </div>
        </div>
      </div>

      <!-- 无API密钥状态 -->
      <div v-else class="text-center py-8">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.029 5.912c-.563-.097-1.159-.026-1.658.33L10 17H6v4H2v-4l8.343-8.343c.356-.499.427-1.095.33-1.658A6 6 0 1121 9z" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">暂无API密钥</h3>
        <p class="mt-1 text-sm text-gray-500">点击下方按钮生成API密钥</p>
      </div>

      <!-- 操作按钮 -->
      <div class="flex flex-col sm:flex-row gap-3">
        <UiButton
          v-if="!apiStore.hasApiKey"
          :loading="apiStore.isLoading"
          @click="generateApiKey"
        >
          <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          生成API密钥
        </UiButton>

        <template v-else>
          <UiButton
            variant="warning"
            :loading="apiStore.isLoading"
            @click="showRegenerateConfirm = true"
          >
            <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            重新生成
          </UiButton>

          <UiButton
            variant="secondary"
            @click="testApiKey"
          >
            <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            测试连接
          </UiButton>
        </template>
      </div>

      <!-- 错误信息 -->
      <div v-if="apiStore.error" class="bg-red-50 border border-red-200 rounded-lg p-4">
        <div class="flex">
          <svg class="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-red-800">操作失败</h3>
            <p class="mt-1 text-sm text-red-700">{{ apiStore.error }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 重新生成确认对话框 -->
    <UiModal
      v-model:show="showRegenerateConfirm"
      title="重新生成API密钥"
      size="sm"
    >
      <div class="space-y-4">
        <div class="flex items-start">
          <svg class="h-6 w-6 text-yellow-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-gray-900">确认重新生成</h3>
            <p class="mt-2 text-sm text-gray-600">
              重新生成API密钥后，旧密钥将立即失效。请确保更新所有使用该密钥的应用程序。
            </p>
          </div>
        </div>
      </div>

      <template #footer>
        <UiButton variant="secondary" @click="showRegenerateConfirm = false">
          取消
        </UiButton>
        <UiButton
          variant="warning"
          :loading="apiStore.isLoading"
          @click="regenerateApiKey"
        >
          确认重新生成
        </UiButton>
      </template>
    </UiModal>
  </UiCard>
</template>

<script setup lang="ts">
const apiStore = useApiStore()
const { success, error: showError } = useNotification()

// 响应式数据
const showFullKey = ref(false)
const showRegenerateConfirm = ref(false)

// 计算属性
const baseUrl = computed(() => {
  if (process.client) {
    return `${window.location.protocol}//${window.location.host}`
  }
  return 'http://localhost:3000'
})

// 方法
const toggleKeyVisibility = () => {
  showFullKey.value = !showFullKey.value
}

const copyApiKey = async () => {
  try {
    await apiStore.copyApiKey()
    success('复制成功', 'API密钥已复制到剪贴板')
  } catch (err) {
    showError('复制失败', '无法复制到剪贴板，请手动复制')
  }
}

const generateApiKey = async () => {
  try {
    await apiStore.fetchApiKey()
    success('生成成功', 'API密钥已生成')
  } catch (err) {
    showError('生成失败', '无法生成API密钥')
  }
}

const regenerateApiKey = async () => {
  try {
    await apiStore.regenerateApiKey()
    showRegenerateConfirm.value = false
    success('重新生成成功', '新的API密钥已生成，旧密钥已失效')
  } catch (err) {
    showError('重新生成失败', '无法重新生成API密钥')
  }
}

const testApiKey = async () => {
  if (!apiStore.apiKey?.key) {
    showError('测试失败', '没有可用的API密钥')
    return
  }

  try {
    const response = await $fetch('/api/v1/health', {
      headers: {
        'X-API-KEY': apiStore.apiKey.key
      }
    })

    if (response.success) {
      success('连接测试成功', 'API密钥工作正常')
    } else {
      showError('连接测试失败', 'API响应异常')
    }
  } catch (err: any) {
    showError('连接测试失败', err.message || '无法连接到API服务')
  }
}

// 生命周期
onMounted(async () => {
  // 只在客户端执行API调用
  if (process.client && !apiStore.hasApiKey) {
    try {
      await apiStore.fetchApiKey()
    } catch (err) {
      // 忽略初始加载错误
    }
  }
})
</script>
