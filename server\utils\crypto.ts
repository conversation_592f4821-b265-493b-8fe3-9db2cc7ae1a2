import crypto from 'crypto'

/**
 * 生成API密钥
 * 格式: ep_[32位随机字符串]
 */
export function generateApiKey(): string {
  const randomBytes = crypto.randomBytes(24) // 24字节 = 32个base64字符（去掉填充）
  const randomString = randomBytes.toString('base64')
    .replace(/\+/g, '')  // 移除 + 字符
    .replace(/\//g, '')  // 移除 / 字符
    .replace(/=/g, '')   // 移除填充字符
    .substring(0, 32)    // 确保长度为32
  
  return `ep_${randomString}`
}

/**
 * 生成随机字符串
 */
export function generateRandomString(length: number = 32): string {
  return crypto.randomBytes(Math.ceil(length / 2)).toString('hex').substring(0, length)
}

/**
 * 生成UUID v4
 */
export function generateUUID(): string {
  return crypto.randomUUID()
}

/**
 * 验证API密钥格式
 */
export function isValidApiKeyFormat(key: string): boolean {
  return /^ep_[A-Za-z0-9]{32}$/.test(key)
}
