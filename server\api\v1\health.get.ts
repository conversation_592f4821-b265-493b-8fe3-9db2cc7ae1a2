/**
 * 健康检查接口
 * GET /api/v1/health
 * 
 * 用于检查系统健康状态，不需要API密钥认证
 */

import { checkDatabaseConnection, getDatabaseStats } from '../../utils/db'

export default defineEventHandler(async (event) => {
  try {
    const startTime = Date.now()

    // 1. 检查数据库连接
    const isDatabaseHealthy = await checkDatabaseConnection()

    // 2. 获取基础统计信息
    let stats = null
    if (isDatabaseHealthy) {
      try {
        stats = await getDatabaseStats()
      } catch (error) {
        console.warn('Failed to get database stats for health check:', error)
      }
    }

    // 3. 计算响应时间
    const responseTime = Date.now() - startTime

    // 4. 确定整体健康状态
    const isHealthy = isDatabaseHealthy

    return {
      success: true,
      data: {
        status: isHealthy ? 'healthy' : 'unhealthy',
        timestamp: new Date().toISOString(),
        responseTime: `${responseTime}ms`,
        checks: {
          database: {
            status: isDatabaseHealthy ? 'ok' : 'error',
            message: isDatabaseHealthy ? 'Database connection successful' : 'Database connection failed'
          }
        },
        ...(stats && {
          stats: {
            poolTypes: stats.poolTypes,
            pools: stats.pools,
            accounts: stats.accounts,
            availableAccounts: stats.availableAccounts,
            usedAccounts: stats.usedAccounts,
            invalidAccounts: stats.invalidAccounts,
            expiredAccounts: stats.expiredAccounts,
            apiKeys: stats.apiKeys
          }
        }),
        version: '1.0.0',
        environment: process.env.NODE_ENV || 'development'
      }
    }
  } catch (error) {
    console.error('Health check failed:', error)
    
    return {
      success: false,
      data: {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: 'Health check failed',
        checks: {
          database: {
            status: 'error',
            message: 'Health check failed'
          }
        },
        version: '1.0.0',
        environment: process.env.NODE_ENV || 'development'
      }
    }
  }
})
