name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  NODE_VERSION: '18'

jobs:
  # 代码质量检查
  lint:
    name: <PERSON>t and Type Check
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Generate Prisma Client
        run: npx prisma generate

      - name: Run ESLint
        run: npm run lint

      - name: Run TypeScript check
        run: npm run typecheck

  # 构建测试
  build:
    name: Build Application
    runs-on: ubuntu-latest
    needs: lint
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Generate Prisma Client
        run: npx prisma generate

      - name: Build application
        run: npm run build

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: build-files
          path: .output/

  # 单元测试
  test:
    name: Unit Tests
    runs-on: ubuntu-latest
    needs: lint
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: test_password
          MYSQL_DATABASE: test_db
        ports:
          - 3306:3306
        options: >-
          --health-cmd="mysqladmin ping"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=3

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Generate Prisma Client
        run: npx prisma generate

      - name: Run database migrations
        run: npx prisma db push
        env:
          DATABASE_URL: mysql://root:test_password@localhost:3306/test_db

      - name: Run tests
        run: npm run test
        env:
          DATABASE_URL: mysql://root:test_password@localhost:3306/test_db
          NUXT_SECRET_KEY: test-secret-key-minimum-32-characters

  # Docker 构建测试
  docker:
    name: Docker Build Test
    runs-on: ubuntu-latest
    needs: [lint, build]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          push: false
          tags: qnb-pool:test
          cache-from: type=gha
          cache-to: type=gha,mode=max

  # 安全扫描
  security:
    name: Security Scan
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run security audit
        run: npm audit --audit-level=high

      - name: Run Snyk security scan
        uses: snyk/actions/node@master
        continue-on-error: true
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          args: --severity-threshold=high

  # 部署到 Vercel (仅在 main 分支)
  deploy-vercel:
    name: Deploy to Vercel
    runs-on: ubuntu-latest
    needs: [test, build, docker]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Deploy to Vercel
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          vercel-args: '--prod'

  # 部署到生产服务器 (仅在 main 分支)
  deploy-server:
    name: Deploy to Production Server
    runs-on: ubuntu-latest
    needs: [test, build, docker]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    steps:
      - name: Deploy to server
        uses: appleboy/ssh-action@v1.0.0
        with:
          host: ${{ secrets.HOST }}
          username: ${{ secrets.USERNAME }}
          key: ${{ secrets.SSH_KEY }}
          script: |
            cd /var/www/qnb-pool
            git pull origin main
            npm ci --production
            npx prisma generate
            npx prisma db push
            npm run build
            pm2 reload ecosystem.config.js --env production

  # 通知
  notify:
    name: Notify Deployment
    runs-on: ubuntu-latest
    needs: [deploy-vercel, deploy-server]
    if: always() && github.ref == 'refs/heads/main'
    steps:
      - name: Notify success
        if: needs.deploy-vercel.result == 'success' || needs.deploy-server.result == 'success'
        run: echo "Deployment successful!"

      - name: Notify failure
        if: needs.deploy-vercel.result == 'failure' || needs.deploy-server.result == 'failure'
        run: echo "Deployment failed!"
