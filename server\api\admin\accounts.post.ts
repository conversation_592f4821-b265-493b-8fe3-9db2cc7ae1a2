/**
 * 添加单个账号接口
 * POST /api/admin/accounts
 */

import { prisma } from '../../utils/db'

export default defineEventHandler(async (event) => {
  try {
    // 获取请求体数据
    const body = await readBody(event)
    
    // 数据验证
    if (!body || !body.content || !body.poolId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Account content and pool ID are required'
      })
    }

    const { content, poolId, notes, expiresAt } = body

    // 验证账号内容
    if (typeof content !== 'string' || content.trim().length === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Account content must be a non-empty string'
      })
    }

    const trimmedContent = content.trim()

    // 验证内容长度
    if (trimmedContent.length > 5000) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Account content must be 5000 characters or less'
      })
    }

    // 验证号池ID格式
    if (typeof poolId !== 'string' || poolId.length < 10) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid pool ID format'
      })
    }

    // 检查号池是否存在
    const pool = await prisma.pool.findUnique({
      where: { id: poolId },
      include: {
        poolType: true
      }
    })

    if (!pool) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Pool not found'
      })
    }

    // 验证过期时间（如果提供）
    let parsedExpiresAt = null
    if (expiresAt) {
      parsedExpiresAt = new Date(expiresAt)
      if (isNaN(parsedExpiresAt.getTime())) {
        throw createError({
          statusCode: 400,
          statusMessage: 'Invalid expiration date format'
        })
      }
      
      // 检查过期时间是否在未来
      if (parsedExpiresAt <= new Date()) {
        throw createError({
          statusCode: 400,
          statusMessage: 'Expiration date must be in the future'
        })
      }
    }

    // 验证备注长度（如果提供）
    if (notes && typeof notes === 'string' && notes.length > 1000) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Notes must be 1000 characters or less'
      })
    }

    // 创建新账号
    const newAccount = await prisma.account.create({
      data: {
        content: trimmedContent,
        poolId,
        notes: notes || null,
        expiresAt: parsedExpiresAt,
        status: 'Available'
      }
    })

    console.log(`New account created in pool: ${pool.name} (ID: ${newAccount.id})`)

    return {
      success: true,
      message: 'Account created successfully',
      data: {
        id: newAccount.id,
        content: newAccount.content,
        status: newAccount.status,
        notes: newAccount.notes,
        expiresAt: newAccount.expiresAt,
        poolId: newAccount.poolId,
        poolName: pool.name,
        poolTypeName: pool.poolType.name,
        createdAt: newAccount.createdAt
      }
    }
  } catch (error) {
    console.error('Failed to create account:', error)
    
    // 如果是已知的错误（如验证错误），直接抛出
    if (error.statusCode) {
      throw error
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to create account'
    })
  }
})
