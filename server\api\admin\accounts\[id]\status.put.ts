/**
 * 更新账号状态接口
 * PUT /api/admin/accounts/[id]/status
 */

import { prisma } from '../../../../utils/db'

export default defineEventHandler(async (event) => {
  try {
    // 获取路由参数中的ID
    const id = getRouterParam(event, 'id')
    
    if (!id) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Account ID is required'
      })
    }

    // 验证ID格式
    if (typeof id !== 'string' || id.length < 10) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid account ID format'
      })
    }

    // 获取请求体数据
    const body = await readBody(event)
    
    if (!body || !body.status) {
      throw createError({
        statusCode: 400,
        statusMessage: 'New status is required'
      })
    }

    const { status, notes } = body

    // 验证状态值
    const validStatuses = ['Available', 'InUse', 'Invalid', 'Failed', 'Expired']
    if (!validStatuses.includes(status)) {
      throw createError({
        statusCode: 400,
        statusMessage: `Invalid status. Must be one of: ${validStatuses.join(', ')}`
      })
    }

    // 验证备注长度（如果提供）
    if (notes && typeof notes === 'string' && notes.length > 1000) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Notes must be 1000 characters or less'
      })
    }

    // 检查账号是否存在
    const account = await prisma.account.findUnique({
      where: { id },
      include: {
        pool: {
          include: {
            poolType: true
          }
        }
      }
    })

    if (!account) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Account not found'
      })
    }

    // 检查状态转换是否合理
    const currentStatus = account.status
    const now = new Date()

    // 如果账号已过期，允许重新激活（设置为Available），但不允许直接设置为InUse
    if (account.expiresAt && account.expiresAt <= now && status === 'InUse') {
      throw createError({
        statusCode: 400,
        statusMessage: 'Cannot set expired account directly to InUse status. Please reactivate first.'
      })
    }

    // 更新账号状态
    const updateData: any = {
      status,
      lastUsedAt: now
    }

    // 如果提供了备注，也更新备注
    if (notes !== undefined) {
      updateData.notes = notes || null
    }

    const updatedAccount = await prisma.account.update({
      where: { id },
      data: updateData
    })

    console.log(`Account status updated: ${id} from ${currentStatus} to ${status}`)

    return {
      success: true,
      message: 'Account status updated successfully',
      data: {
        id: updatedAccount.id,
        previousStatus: currentStatus,
        newStatus: updatedAccount.status,
        notes: updatedAccount.notes,
        poolId: account.poolId,
        poolName: account.pool.name,
        poolTypeName: account.pool.poolType.name,
        updatedAt: updatedAccount.lastUsedAt,
        expiresAt: updatedAccount.expiresAt
      }
    }
  } catch (error) {
    console.error('Failed to update account status:', error)
    
    // 如果是已知的错误（如验证错误），直接抛出
    if (error.statusCode) {
      throw error
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to update account status'
    })
  }
})
