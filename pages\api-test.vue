<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 页面头部 -->
    <div class="bg-white shadow-sm border-b">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between h-16">
          <div class="flex items-center">
            <h1 class="text-2xl font-bold text-gray-900">API测试</h1>
            <span class="ml-2 text-sm text-gray-500">核心业务API接口测试</span>
          </div>
          
          <div class="flex items-center gap-2">
            <UiButton
              size="sm"
              variant="ghost"
              @click="$router.push('/')"
            >
              <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
              </svg>
              返回首页
            </UiButton>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- API密钥管理 -->
      <div class="mb-8">
        <ClientOnly>
          <ApiKeyManager />
          <template #fallback>
            <UiCard title="API密钥管理" subtitle="管理系统API访问密钥">
              <div class="flex items-center justify-center py-8">
                <div class="text-gray-500">加载中...</div>
              </div>
            </UiCard>
          </template>
        </ClientOnly>
      </div>

      <!-- API测试区域 -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- 取号测试 -->
        <UiCard title="取号测试" subtitle="测试获取可用账号的API">
          <div class="space-y-4">
            <!-- 参数设置 -->
            <div class="grid grid-cols-1 gap-4">
              <UiInput
                v-model="getAccountParams.poolName"
                label="号池名称"
                placeholder="输入号池名称（推荐使用）"
                hint="号池名称是唯一的，推荐使用此方式。例如：cursor"
              />

              <UiInput
                v-model="getAccountParams.poolType"
                label="号池类型"
                placeholder="输入号池类型名称（可选）"
                hint="可选参数，当不指定号池名称时使用。例如：AI编辑器"
              />
            </div>

            <!-- 测试按钮 -->
            <UiButton
              :loading="getAccountLoading"
              :disabled="!apiStore.hasApiKey || (!getAccountParams.poolName && !getAccountParams.poolType)"
              block
              @click="testGetAccount"
            >
              <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              获取账号
            </UiButton>

            <!-- 结果显示 -->
            <div v-if="getAccountResult" class="mt-4">
              <h4 class="text-sm font-medium text-gray-900 mb-2">响应结果：</h4>
              <div class="bg-gray-50 rounded-lg p-3">
                <pre class="text-xs text-gray-700 whitespace-pre-wrap">{{ JSON.stringify(getAccountResult, null, 2) }}</pre>
              </div>
            </div>
          </div>
        </UiCard>

        <!-- 归还测试 -->
        <UiCard title="归还测试" subtitle="测试上报账号使用结果的API">
          <div class="space-y-4">
            <!-- 参数设置 -->
            <div class="grid grid-cols-1 gap-4">
              <UiInput
                v-model="reportAccountParams.accountId"
                label="账号ID"
                placeholder="输入账号ID"
                required
              />
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  使用结果
                </label>
                <select
                  v-model="reportAccountParams.result"
                  class="block w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm"
                >
                  <option value="ok">成功 (ok)</option>
                  <option value="failed">失败 (failed)</option>
                </select>
              </div>
              
              <UiInput
                v-model="reportAccountParams.notes"
                label="备注"
                placeholder="可选的备注信息"
              />
            </div>

            <!-- 测试按钮 -->
            <UiButton
              :loading="reportAccountLoading"
              :disabled="!apiStore.hasApiKey || !reportAccountParams.accountId"
              block
              @click="testReportAccount"
            >
              <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              上报结果
            </UiButton>

            <!-- 结果显示 -->
            <div v-if="reportAccountResult" class="mt-4">
              <h4 class="text-sm font-medium text-gray-900 mb-2">响应结果：</h4>
              <div class="bg-gray-50 rounded-lg p-3">
                <pre class="text-xs text-gray-700 whitespace-pre-wrap">{{ JSON.stringify(reportAccountResult, null, 2) }}</pre>
              </div>
            </div>
          </div>
        </UiCard>

        <!-- 统计信息测试 -->
        <UiCard title="统计信息测试" subtitle="测试获取API使用统计的接口">
          <div class="space-y-4">
            <!-- 参数设置 -->
            <div class="grid grid-cols-1 gap-4">
              <UiInput
                v-model="getStatsParams.poolName"
                label="号池名称"
                placeholder="可选，筛选特定号池"
              />
              
              <UiInput
                v-model="getStatsParams.poolType"
                label="号池类型"
                placeholder="可选，筛选特定类型"
              />
            </div>

            <!-- 测试按钮 -->
            <UiButton
              :loading="getStatsLoading"
              :disabled="!apiStore.hasApiKey"
              block
              @click="testGetStats"
            >
              <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
              获取统计
            </UiButton>

            <!-- 结果显示 -->
            <div v-if="getStatsResult" class="mt-4">
              <h4 class="text-sm font-medium text-gray-900 mb-2">响应结果：</h4>
              <div class="bg-gray-50 rounded-lg p-3 max-h-64 overflow-y-auto">
                <pre class="text-xs text-gray-700 whitespace-pre-wrap">{{ JSON.stringify(getStatsResult, null, 2) }}</pre>
              </div>
            </div>
          </div>
        </UiCard>

        <!-- 健康检查测试 -->
        <UiCard title="健康检查测试" subtitle="测试系统健康状态检查接口">
          <div class="space-y-4">
            <p class="text-sm text-gray-600">
              健康检查接口不需要API密钥，用于监控系统状态。
            </p>

            <!-- 测试按钮 -->
            <UiButton
              :loading="healthCheckLoading"
              block
              @click="testHealthCheck"
            >
              <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
              </svg>
              健康检查
            </UiButton>

            <!-- 结果显示 -->
            <div v-if="healthCheckResult" class="mt-4">
              <h4 class="text-sm font-medium text-gray-900 mb-2">响应结果：</h4>
              <div class="bg-gray-50 rounded-lg p-3">
                <pre class="text-xs text-gray-700 whitespace-pre-wrap">{{ JSON.stringify(healthCheckResult, null, 2) }}</pre>
              </div>
            </div>
          </div>
        </UiCard>
      </div>

      <!-- 使用说明 -->
      <UiCard title="API使用说明" class="mt-8">
        <div class="prose prose-sm max-w-none">
          <h4>核心业务API端点：</h4>
          <ul>
            <li><strong>GET /api/v1/account</strong> - 获取可用账号（取号）</li>
            <li><strong>POST /api/v1/account/report</strong> - 上报账号使用结果（归还）</li>
            <li><strong>GET /api/v1/stats</strong> - 获取API使用统计信息</li>
            <li><strong>GET /api/v1/health</strong> - 系统健康检查（无需API密钥）</li>
          </ul>
          
          <h4>认证方式：</h4>
          <p>在请求头中添加：<code>X-API-KEY: your_api_key</code></p>
          
          <h4>错误处理：</h4>
          <p>所有API都会返回标准的HTTP状态码和JSON格式的错误信息。</p>
        </div>
      </UiCard>
    </div>

    <!-- 通知组件 -->
    <UiNotification />
  </div>
</template>

<script setup lang="ts">
// 页面元数据
useHead({
  title: 'API测试 - EasyPool'
})

// 状态管理
const apiStore = useApiStore()
const { success, error } = useNotification()

// 响应式数据
const getAccountLoading = ref(false)
const reportAccountLoading = ref(false)
const getStatsLoading = ref(false)
const healthCheckLoading = ref(false)

const getAccountParams = ref({
  poolName: '',
  poolType: ''
})

const reportAccountParams = ref({
  accountId: '',
  result: 'ok',
  notes: ''
})

const getStatsParams = ref({
  poolName: '',
  poolType: ''
})

const getAccountResult = ref(null)
const reportAccountResult = ref(null)
const getStatsResult = ref(null)
const healthCheckResult = ref(null)

// 方法
const testGetAccount = async () => {
  if (!apiStore.apiKey?.key) {
    error('测试失败', '请先生成API密钥')
    return
  }

  getAccountLoading.value = true
  getAccountResult.value = null

  try {
    const params = {
      ...(getAccountParams.value.poolName && { poolName: getAccountParams.value.poolName }),
      ...(getAccountParams.value.poolType && { poolType: getAccountParams.value.poolType })
    }

    const result = await coreApi.getAccount(params, apiStore.apiKey.key)
    getAccountResult.value = result
    success('测试成功', '账号获取成功')
  } catch (err: any) {
    getAccountResult.value = { error: err.message }
    error('测试失败', err.message)
  } finally {
    getAccountLoading.value = false
  }
}

const testReportAccount = async () => {
  if (!apiStore.apiKey?.key) {
    error('测试失败', '请先生成API密钥')
    return
  }

  reportAccountLoading.value = true
  reportAccountResult.value = null

  try {
    const result = await coreApi.reportAccount(reportAccountParams.value, apiStore.apiKey.key)
    reportAccountResult.value = result
    success('测试成功', '账号状态上报成功')
  } catch (err: any) {
    reportAccountResult.value = { error: err.message }
    error('测试失败', err.message)
  } finally {
    reportAccountLoading.value = false
  }
}

const testGetStats = async () => {
  if (!apiStore.apiKey?.key) {
    error('测试失败', '请先生成API密钥')
    return
  }

  getStatsLoading.value = true
  getStatsResult.value = null

  try {
    const filters = {
      ...(getStatsParams.value.poolName && { poolName: getStatsParams.value.poolName }),
      ...(getStatsParams.value.poolType && { poolType: getStatsParams.value.poolType })
    }

    const result = await coreApi.getStats(apiStore.apiKey.key, filters)
    getStatsResult.value = result
    success('测试成功', '统计信息获取成功')
  } catch (err: any) {
    getStatsResult.value = { error: err.message }
    error('测试失败', err.message)
  } finally {
    getStatsLoading.value = false
  }
}

const testHealthCheck = async () => {
  healthCheckLoading.value = true
  healthCheckResult.value = null

  try {
    const result = await coreApi.healthCheck()
    healthCheckResult.value = result
    success('测试成功', '系统健康检查完成')
  } catch (err: any) {
    healthCheckResult.value = { error: err.message }
    error('测试失败', err.message)
  } finally {
    healthCheckLoading.value = false
  }
}
</script>
