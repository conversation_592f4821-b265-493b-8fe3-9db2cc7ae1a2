/**
 * 全局TypeScript类型定义
 * 定义系统中使用的所有数据类型和接口
 */

// 基础响应类型
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: string
  total?: number
}

// 分页相关类型
export interface PaginationParams {
  page?: number
  limit?: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export interface PaginationInfo {
  currentPage: number
  totalPages: number
  totalCount: number
  limit: number
  hasNextPage: boolean
  hasPrevPage: boolean
}

// 账号状态枚举
export type AccountStatus = 'Available' | 'InUse' | 'Invalid' | 'Failed' | 'Expired'

// 号池类型
export interface PoolType {
  id: string
  name: string
  pools?: Pool[]
  stats?: PoolTypeStats
}

export interface PoolTypeStats {
  totalPools: number
  totalAccounts: number
  availableAccounts: number
  inUseAccounts: number
  invalidAccounts: number
  expiredAccounts: number
}

// 号池
export interface Pool {
  id: string
  name: string
  description?: string
  poolTypeId: string
  poolType?: PoolType
  accounts?: Account[]
  stats?: PoolStats
  createdAt?: string
  updatedAt?: string
}

export interface PoolStats {
  total: number
  available: number
  inUse: number
  invalid: number
  expired: number
}

// 账号
export interface Account {
  id: string
  content: string
  status: AccountStatus
  notes?: string | null
  occupiedBy?: string | null
  expiresAt?: string | null
  poolId: string
  pool?: Pool
  createdAt: string
  updatedAt?: string
  lastUsedAt?: string | null
}

// API密钥
export interface ApiKey {
  id: number
  name: string
  description?: string
  key: string
  maskedKey?: string
  status: 'Active' | 'Disabled'
  expiresAt?: string | null
  createdAt: string
  updatedAt?: string
}

// 表单相关类型
export interface CreatePoolTypeForm {
  name: string
}

export interface CreatePoolForm {
  name: string
  description?: string
  poolTypeId: string
}

export interface CreateAccountForm {
  content: string
  poolId: string
  notes?: string
  expiresAt?: string
}

export interface BatchImportForm {
  poolId: string
  accounts: Array<{
    content: string
    notes?: string
    expiresAt?: string
  }>
}

export interface UpdateAccountStatusForm {
  status: AccountStatus
  notes?: string
}

// 搜索和筛选类型
export interface AccountFilters {
  status?: AccountStatus
  search?: string
  poolId?: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export interface PoolFilters {
  poolTypeId?: string
  groupByType?: boolean
  search?: string
}

// 统计信息类型
export interface SystemStats {
  summary: {
    totalAccounts: number
    usageRate: number
    availabilityRate: number
    recentlyUsedAccounts: number
    expiredAccountsNeedingUpdate: number
  }
  statusBreakdown: {
    available: number
    inUse: number
    invalid: number
    expired: number
  }
  pools?: Array<{
    poolId: string
    poolName: string
    poolType: string
    totalAccounts: number
  }>
  timestamp: string
}

// 核心API相关类型
export interface GetAccountParams {
  poolName?: string
  poolType?: string
  userId?: string // 用户标识，用于账号占用跟踪（通常为API密钥）
}

export interface GetAccountResponse {
  accountId: string
  content: string
  poolName: string
  poolType: string
  expiresAt?: string | null
  retrievedAt: string
  notes?: string | null
}

export interface ReportAccountParams {
  accountId: string
  result: 'ok' | 'failed'
  notes?: string
}

export interface ReportAccountResponse {
  accountId: string
  previousStatus: AccountStatus
  newStatus: AccountStatus
  result: 'ok' | 'failed'
  poolName: string
  poolType: string
  reportedAt: string
  wasExpired: boolean
  notes?: string | null
}

// 健康检查类型
export interface HealthCheckResponse {
  status: 'healthy' | 'unhealthy'
  timestamp: string
  responseTime: string
  checks: {
    database: {
      status: 'ok' | 'error'
      message: string
    }
  }
  stats?: {
    poolTypes: number
    pools: number
    accounts: number
    apiKeys: number
  }
  version: string
  environment: string
}

// 错误类型
export interface ApiError {
  statusCode: number
  statusMessage: string
  message: string
  data?: any
}

// 组件Props类型
export interface TableColumn<T = any> {
  key: string
  title: string
  sortable?: boolean
  width?: string
  align?: 'left' | 'center' | 'right'
  render?: (value: any, row: T) => string
}

export interface ModalProps {
  show: boolean
  title: string
  size?: 'sm' | 'md' | 'lg' | 'xl'
  persistent?: boolean
}

export interface ButtonProps {
  variant?: 'primary' | 'secondary' | 'danger' | 'success' | 'warning' | 'info'
  size?: 'sm' | 'md' | 'lg'
  loading?: boolean
  disabled?: boolean
  block?: boolean
}

// 工具函数类型
export type DateFormat = 'date' | 'datetime' | 'time' | 'relative'

export interface FormatOptions {
  locale?: string
  timezone?: string
}
