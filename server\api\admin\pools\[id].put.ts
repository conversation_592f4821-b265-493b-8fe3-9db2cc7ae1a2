/**
 * 更新号池接口
 * PUT /api/admin/pools/[id]
 */

import { prisma } from '../../../utils/db'

export default defineEventHandler(async (event) => {
  try {
    // 获取路由参数中的ID
    const id = getRouterParam(event, 'id')
    
    if (!id) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Pool ID is required'
      })
    }

    // 验证ID格式
    if (typeof id !== 'string' || id.length < 10) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid pool ID format'
      })
    }

    // 获取请求体数据
    const body = await readBody(event)
    
    // 数据验证
    if (!body || (!body.name && !body.poolTypeId)) {
      throw createError({
        statusCode: 400,
        statusMessage: 'At least one field (name or poolTypeId) is required for update'
      })
    }

    const { name, poolTypeId } = body
    const updateData: any = {}

    // 验证并处理名称更新
    if (name !== undefined) {
      if (typeof name !== 'string' || name.trim().length === 0) {
        throw createError({
          statusCode: 400,
          statusMessage: 'Pool name must be a non-empty string'
        })
      }

      const trimmedName = name.trim()

      if (trimmedName.length > 50) {
        throw createError({
          statusCode: 400,
          statusMessage: 'Pool name must be 50 characters or less'
        })
      }

      // 检查新名称是否与其他号池冲突
      const nameConflict = await prisma.pool.findFirst({
        where: {
          name: trimmedName,
          id: { not: id }
        }
      })

      if (nameConflict) {
        throw createError({
          statusCode: 409,
          statusMessage: 'Pool with this name already exists'
        })
      }

      updateData.name = trimmedName
    }

    // 验证并处理号池类型更新
    if (poolTypeId !== undefined) {
      if (typeof poolTypeId !== 'string' || poolTypeId.length < 10) {
        throw createError({
          statusCode: 400,
          statusMessage: 'Invalid pool type ID format'
        })
      }

      // 检查号池类型是否存在
      const poolType = await prisma.poolType.findUnique({
        where: { id: poolTypeId }
      })

      if (!poolType) {
        throw createError({
          statusCode: 404,
          statusMessage: 'Pool type not found'
        })
      }

      updateData.poolTypeId = poolTypeId
    }

    // 检查号池是否存在
    const existingPool = await prisma.pool.findUnique({
      where: { id },
      include: {
        poolType: true
      }
    })

    if (!existingPool) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Pool not found'
      })
    }

    // 更新号池
    const updatedPool = await prisma.pool.update({
      where: { id },
      data: updateData,
      include: {
        poolType: true
      }
    })

    console.log(`Pool updated: ${existingPool.name} -> ${updatedPool.name} (ID: ${id})`)
    if (poolTypeId && existingPool.poolType.id !== updatedPool.poolType.id) {
      console.log(`Pool type changed: ${existingPool.poolType.name} -> ${updatedPool.poolType.name}`)
    }

    return {
      success: true,
      message: 'Pool updated successfully',
      data: {
        id: updatedPool.id,
        name: updatedPool.name,
        poolType: {
          id: updatedPool.poolType.id,
          name: updatedPool.poolType.name
        },
        previousName: existingPool.name,
        previousPoolType: {
          id: existingPool.poolType.id,
          name: existingPool.poolType.name
        }
      }
    }
  } catch (error) {
    console.error('Failed to update pool:', error)
    
    // 如果是已知的错误（如验证错误），直接抛出
    if (error.statusCode) {
      throw error
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to update pool'
    })
  }
})
