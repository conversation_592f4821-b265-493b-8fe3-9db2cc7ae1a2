/**
 * 获取当前API密钥接口
 * GET /api/admin/apikey
 */

import { prisma } from '../../utils/db'

export default defineEventHandler(async (event) => {
  try {
    // 获取当前API密钥
    const apiKey = await prisma.apiKey.findFirst({
      orderBy: {
        createdAt: 'desc'
      }
    })

    if (!apiKey) {
      throw createError({
        statusCode: 404,
        statusMessage: 'API key not found'
      })
    }

    return {
      success: true,
      data: {
        id: apiKey.id,
        key: apiKey.key,
        createdAt: apiKey.createdAt,
        // 提供部分脱敏的密钥用于显示
        maskedKey: `${apiKey.key.substring(0, 8)}...${apiKey.key.substring(apiKey.key.length - 4)}`
      }
    }
  } catch (error) {
    console.error('Failed to get API key:', error)
    
    if (error.statusCode) {
      throw error
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error'
    })
  }
})
