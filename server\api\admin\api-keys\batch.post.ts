import { z } from 'zod'
import { prisma } from '~/server/utils/db'
import { requireAdminAuth } from '~/server/utils/auth'
import { generateApiKey } from '~/server/utils/crypto'

// 验证批量创建请求的schema
const batchCreateSchema = z.object({
  apiKeys: z.array(z.object({
    name: z.string().min(1).max(100),
    description: z.string().optional(),
    expiresAt: z.string().datetime().optional().nullable(),
    userId: z.string().optional().nullable(),
    permissions: z.array(z.string()).optional().nullable()
  })).min(1).max(100) // 最多一次创建100个
})

export default defineEventHandler(async (event) => {
  try {
    // 验证管理员权限
    await requireAdminAuth(event)

    // 验证请求体
    const body = await readBody(event)
    const { apiKeys } = batchCreateSchema.parse(body)

    const createdApiKeys = []
    const errors = []

    // 批量创建API密钥
    for (let i = 0; i < apiKeys.length; i++) {
      const apiKeyData = apiKeys[i]
      
      try {
        // 检查名称是否已存在（通过查询所有记录来检查）
        const existingApiKey = await prisma.apiKey.findFirst({
          where: { name: apiKeyData.name }
        })

        if (existingApiKey) {
          errors.push({
            index: i,
            name: apiKeyData.name,
            error: '名称已存在'
          })
          continue
        }

        // 验证用户ID（如果提供）
        if (apiKeyData.userId) {
          const user = await prisma.user.findUnique({
            where: { id: apiKeyData.userId }
          })
          if (!user) {
            errors.push({
              index: i,
              name: apiKeyData.name,
              error: '关联用户不存在'
            })
            continue
          }
        }

        // 生成API密钥
        const key = generateApiKey()

        // 创建API密钥记录
        const createdApiKey = await prisma.apiKey.create({
          data: {
            name: apiKeyData.name,
            key,
            description: apiKeyData.description || null,
            expiresAt: apiKeyData.expiresAt ? new Date(apiKeyData.expiresAt) : null,
            userId: apiKeyData.userId || null,
            permissions: apiKeyData.permissions ? JSON.stringify(apiKeyData.permissions) : null,
            status: 'Active'
          },
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true
              }
            }
          }
        })

        createdApiKeys.push(createdApiKey)
      } catch (error) {
        console.error(`Error creating API key ${apiKeyData.name}:`, error)
        errors.push({
          index: i,
          name: apiKeyData.name,
          error: error.message || '创建失败'
        })
      }
    }

    // 返回结果
    return {
      success: true,
      message: `成功创建 ${createdApiKeys.length} 个API密钥`,
      apiKeys: createdApiKeys,
      errors: errors.length > 0 ? errors : undefined,
      summary: {
        total: apiKeys.length,
        success: createdApiKeys.length,
        failed: errors.length
      }
    }

  } catch (error) {
    console.error('Batch create API keys error:', error)
    
    if (error.name === 'ZodError') {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid request data',
        data: error.errors
      })
    }

    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to batch create API keys'
    })
  }
})
