/**
 * 批量导入账号接口
 * POST /api/admin/accounts/batch
 */

import { prisma, executeTransaction } from '../../../utils/db'

export default defineEventHandler(async (event) => {
  try {
    // 获取请求体数据
    const body = await readBody(event)
    
    // 数据验证
    if (!body || !body.accounts || !body.poolId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Accounts array and pool ID are required'
      })
    }

    const { accounts, poolId } = body

    // 验证账号数组
    if (!Array.isArray(accounts) || accounts.length === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Accounts must be a non-empty array'
      })
    }

    // 限制批量导入数量
    if (accounts.length > 1000) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Cannot import more than 1000 accounts at once'
      })
    }

    // 验证号池ID格式
    if (typeof poolId !== 'string' || poolId.length < 10) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid pool ID format'
      })
    }

    // 检查号池是否存在
    const pool = await prisma.pool.findUnique({
      where: { id: poolId },
      include: {
        poolType: true
      }
    })

    if (!pool) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Pool not found'
      })
    }

    // 验证每个账号数据
    const validatedAccounts = []
    const errors = []

    for (let i = 0; i < accounts.length; i++) {
      const account = accounts[i]
      const index = i + 1

      try {
        // 验证账号内容
        if (!account.content || typeof account.content !== 'string' || account.content.trim().length === 0) {
          throw new Error(`Account ${index}: Content is required and must be a non-empty string`)
        }

        const trimmedContent = account.content.trim()

        if (trimmedContent.length > 5000) {
          throw new Error(`Account ${index}: Content must be 5000 characters or less`)
        }

        // 验证过期时间（如果提供）
        let parsedExpiresAt = null
        if (account.expiresAt) {
          parsedExpiresAt = new Date(account.expiresAt)
          if (isNaN(parsedExpiresAt.getTime())) {
            throw new Error(`Account ${index}: Invalid expiration date format`)
          }
          
          if (parsedExpiresAt <= new Date()) {
            throw new Error(`Account ${index}: Expiration date must be in the future`)
          }
        }

        // 验证备注长度（如果提供）
        if (account.notes && typeof account.notes === 'string' && account.notes.length > 1000) {
          throw new Error(`Account ${index}: Notes must be 1000 characters or less`)
        }

        validatedAccounts.push({
          content: trimmedContent,
          poolId,
          notes: account.notes || null,
          expiresAt: parsedExpiresAt,
          status: 'Available'
        })
      } catch (error) {
        errors.push(error.message)
      }
    }

    // 如果有验证错误，返回错误信息
    if (errors.length > 0) {
      throw createError({
        statusCode: 400,
        statusMessage: `Validation errors: ${errors.join('; ')}`
      })
    }

    // 使用事务批量创建账号
    const result = await executeTransaction(async (tx) => {
      const createdAccounts = await tx.account.createMany({
        data: validatedAccounts
      })

      return createdAccounts
    })

    console.log(`Batch import completed: ${result.count} accounts created in pool: ${pool.name}`)

    return {
      success: true,
      message: `Successfully imported ${result.count} accounts`,
      data: {
        importedCount: result.count,
        totalProvided: accounts.length,
        poolId: poolId,
        poolName: pool.name,
        poolTypeName: pool.poolType.name
      }
    }
  } catch (error) {
    console.error('Failed to batch import accounts:', error)
    
    // 如果是已知的错误（如验证错误），直接抛出
    if (error.statusCode) {
      throw error
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to batch import accounts'
    })
  }
})
