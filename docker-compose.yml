version: '3.8'

services:
  # 应用服务
  app:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=mysql://qnb_user:qnb_password@mysql:3306/qnb_pool
      - NUXT_SECRET_KEY=your-secret-key-minimum-32-characters-long
      - NUXT_PUBLIC_APP_URL=http://localhost:3000
    depends_on:
      mysql:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - qnb-network

  # MySQL 数据库服务
  mysql:
    image: mysql:8.0
    ports:
      - "3306:3306"
    environment:
      - MYSQL_ROOT_PASSWORD=root_password
      - MYSQL_DATABASE=qnb_pool
      - MYSQL_USER=qnb_user
      - MYSQL_PASSWORD=qnb_password
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10
    restart: unless-stopped
    networks:
      - qnb-network

  # Redis 缓存服务（可选）
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      timeout: 3s
      retries: 5
    restart: unless-stopped
    networks:
      - qnb-network

  # Nginx 反向代理（可选）
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - app
    restart: unless-stopped
    networks:
      - qnb-network

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local

networks:
  qnb-network:
    driver: bridge
