/**
 * 批量删除API密钥接口
 * POST /api/admin/api-keys/batch-delete
 */

import { prisma } from '../../../utils/db'

export default defineEventHandler(async (event) => {
  try {
    // 获取请求体
    const body = await readBody(event)
    const { apiKeyIds } = body

    // 验证必填字段
    if (!apiKeyIds || !Array.isArray(apiKeyIds) || apiKeyIds.length === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: 'API key IDs array is required and cannot be empty'
      })
    }

    // 验证API密钥ID格式
    for (const id of apiKeyIds) {
      const numId = parseInt(id)
      if (isNaN(numId)) {
        throw createError({
          statusCode: 400,
          statusMessage: 'Invalid API key ID format'
        })
      }
    }

    // 转换为数字数组
    const numericIds = apiKeyIds.map(id => parseInt(id))

    // 检查API密钥是否存在
    const existingApiKeys = await prisma.apiKey.findMany({
      where: {
        id: {
          in: numericIds
        }
      },
      select: {
        id: true,
        name: true,
        status: true
      }
    })

    if (existingApiKeys.length !== numericIds.length) {
      const foundIds = existingApiKeys.map(key => key.id)
      const notFoundIds = numericIds.filter(id => !foundIds.includes(id))
      throw createError({
        statusCode: 404,
        statusMessage: `Some API keys not found: ${notFoundIds.join(', ')}`
      })
    }

    // 批量删除API密钥
    const deleteResult = await prisma.apiKey.deleteMany({
      where: {
        id: {
          in: numericIds
        }
      }
    })

    return {
      success: true,
      message: `Successfully deleted ${deleteResult.count} API keys`,
      data: {
        deletedCount: deleteResult.count,
        deletedIds: numericIds
      }
    }
  } catch (error) {
    console.error('Failed to batch delete API keys:', error)
    
    // 如果是已知的错误（如验证错误），直接抛出
    if (error.statusCode) {
      throw error
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to batch delete API keys'
    })
  }
})
