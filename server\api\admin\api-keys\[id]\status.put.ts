/**
 * 更新API密钥状态接口
 * PUT /api/admin/api-keys/[id]/status
 */

import { prisma } from '../../../../utils/db'

export default defineEventHandler(async (event) => {
  try {
    // 获取路由参数中的ID
    const apiKeyId = getRouterParam(event, 'id')
    
    if (!apiKeyId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'API key ID is required'
      })
    }

    // 验证ID格式
    const id = parseInt(apiKeyId)
    if (isNaN(id)) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid API key ID format'
      })
    }

    // 获取请求体数据
    const body = await readBody(event)
    
    if (!body || !body.status) {
      throw createError({
        statusCode: 400,
        statusMessage: 'New status is required'
      })
    }

    const { status } = body

    // 验证状态值
    const validStatuses = ['Active', 'Disabled']
    if (!validStatuses.includes(status)) {
      throw createError({
        statusCode: 400,
        statusMessage: `Invalid status. Must be one of: ${validStatuses.join(', ')}`
      })
    }

    // 检查API密钥是否存在
    const existingApiKey = await prisma.apiKey.findUnique({
      where: { id }
    })

    if (!existingApiKey) {
      throw createError({
        statusCode: 404,
        statusMessage: 'API key not found'
      })
    }

    // 检查是否已过期
    const now = new Date()
    if (existingApiKey.expiresAt && existingApiKey.expiresAt <= now && status === 'Active') {
      throw createError({
        statusCode: 400,
        statusMessage: 'Cannot activate expired API key'
      })
    }

    // 更新API密钥状态
    const updatedApiKey = await prisma.apiKey.update({
      where: { id },
      data: {
        status,
        updatedAt: now
      }
    })

    return {
      success: true,
      data: updatedApiKey
    }
  } catch (error) {
    console.error('Failed to update API key status:', error)
    
    // 如果是已知的错误（如验证错误），直接抛出
    if (error.statusCode) {
      throw error
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to update API key status'
    })
  }
})
