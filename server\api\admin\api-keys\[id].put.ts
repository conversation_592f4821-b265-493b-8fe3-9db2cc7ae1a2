/**
 * 更新API密钥接口
 * PUT /api/admin/api-keys/[id]
 */

import { prisma } from '../../../utils/db'

export default defineEventHandler(async (event) => {
  try {
    // 获取路由参数中的ID
    const apiKeyId = getRouterParam(event, 'id')
    
    if (!apiKeyId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'API key ID is required'
      })
    }

    // 验证ID格式
    const id = parseInt(apiKeyId)
    if (isNaN(id)) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid API key ID format'
      })
    }

    // 获取请求体
    const body = await readBody(event)
    const { name, description, expiresAt } = body

    // 验证必填字段
    if (!name || typeof name !== 'string' || name.trim().length === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: 'API key name is required'
      })
    }

    // 验证名称长度
    if (name.trim().length > 100) {
      throw createError({
        statusCode: 400,
        statusMessage: 'API key name must be 100 characters or less'
      })
    }

    // 验证描述长度
    if (description && typeof description === 'string' && description.length > 500) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Description must be 500 characters or less'
      })
    }

    // 检查API密钥是否存在
    const existingApiKey = await prisma.apiKey.findUnique({
      where: { id }
    })

    if (!existingApiKey) {
      throw createError({
        statusCode: 404,
        statusMessage: 'API key not found'
      })
    }

    // 检查名称是否与其他API密钥冲突（排除当前API密钥）
    const duplicateApiKey = await prisma.apiKey.findFirst({
      where: {
        name: name.trim(),
        id: {
          not: id
        }
      }
    })

    if (duplicateApiKey) {
      throw createError({
        statusCode: 409,
        statusMessage: 'API key with this name already exists'
      })
    }

    // 处理过期时间
    let expiresAtDate = null
    if (expiresAt !== undefined) {
      if (expiresAt) {
        expiresAtDate = new Date(expiresAt)
        if (isNaN(expiresAtDate.getTime())) {
          throw createError({
            statusCode: 400,
            statusMessage: 'Invalid expires date format'
          })
        }
      }
    } else {
      // 如果没有提供 expiresAt，保持原值
      expiresAtDate = existingApiKey.expiresAt
    }

    // 更新API密钥
    const updatedApiKey = await prisma.apiKey.update({
      where: { id },
      data: {
        name: name.trim(),
        description: description ? description.trim() : null,
        expiresAt: expiresAtDate,
        updatedAt: new Date()
      }
    })

    return {
      success: true,
      data: updatedApiKey
    }
  } catch (error) {
    console.error('Failed to update API key:', error)
    
    // 如果是已知的错误（如验证错误），直接抛出
    if (error.statusCode) {
      throw error
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to update API key'
    })
  }
})
