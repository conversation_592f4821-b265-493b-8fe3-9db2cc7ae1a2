import { prisma } from '~/server/utils/db'

export default defineEventHandler(async (event) => {
  try {
    // 获取用户token
    const token = getCookie(event, 'user_token')
    
    if (!token) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Not authenticated'
      })
    }

    // 查找有效的会话
    const session = await prisma.userSession.findUnique({
      where: { token },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            status: true,
            createdAt: true,
            lastLoginAt: true
          }
        }
      }
    })

    if (!session) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Invalid session'
      })
    }

    // 检查会话是否过期
    if (session.expiresAt < new Date()) {
      // 删除过期会话
      await prisma.userSession.delete({
        where: { id: session.id }
      })
      
      throw createError({
        statusCode: 401,
        statusMessage: 'Session expired'
      })
    }

    // 检查用户状态
    if (session.user.status !== 'Active') {
      throw createError({
        statusCode: 401,
        statusMessage: 'Account disabled'
      })
    }

    return {
      success: true,
      user: session.user
    }

  } catch (error) {
    console.error('Get user info error:', error.statusMessage || error.message)
    
    if (error.statusCode) {
      throw error
    }

    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to get user info'
    })
  }
})
