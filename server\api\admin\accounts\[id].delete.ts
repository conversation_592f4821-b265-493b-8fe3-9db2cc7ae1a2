/**
 * 删除账号接口
 * DELETE /api/admin/accounts/[id]
 */

import { prisma } from '../../../utils/db'

export default defineEventHandler(async (event) => {
  try {
    // 获取路由参数中的ID
    const id = getRouterParam(event, 'id')
    
    if (!id) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Account ID is required'
      })
    }

    // 验证ID格式
    if (typeof id !== 'string' || id.length < 10) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid account ID format'
      })
    }

    // 检查账号是否存在
    const account = await prisma.account.findUnique({
      where: { id },
      include: {
        pool: {
          include: {
            poolType: true
          }
        }
      }
    })

    if (!account) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Account not found'
      })
    }

    // 检查账号状态，如果正在使用中，给出警告
    if (account.status === 'InUse') {
      console.warn(`Deleting account that is currently in use: ${id}`)
    }

    // 删除账号
    await prisma.account.delete({
      where: { id }
    })

    console.log(`Account deleted: ${id} from pool: ${account.pool.name}`)

    return {
      success: true,
      message: 'Account deleted successfully',
      data: {
        deletedAccount: {
          id: account.id,
          content: account.content.substring(0, 20) + '...', // 部分显示内容
          status: account.status,
          poolId: account.poolId,
          poolName: account.pool.name,
          poolTypeName: account.pool.poolType.name,
          wasInUse: account.status === 'InUse'
        }
      }
    }
  } catch (error) {
    console.error('Failed to delete account:', error)
    
    // 如果是已知的错误（如验证错误），直接抛出
    if (error.statusCode) {
      throw error
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to delete account'
    })
  }
})
