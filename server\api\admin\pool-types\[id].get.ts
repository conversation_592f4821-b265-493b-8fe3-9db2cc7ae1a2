/**
 * 获取单个号池类型详情接口
 * GET /api/admin/pool-types/[id]
 */

import { prisma } from '../../../utils/db'

export default defineEventHandler(async (event) => {
  try {
    // 获取路由参数中的ID
    const id = getRouterParam(event, 'id')
    
    if (!id) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Pool type ID is required'
      })
    }

    // 验证ID格式
    if (typeof id !== 'string' || id.length < 10) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid pool type ID format'
      })
    }

    // 获取号池类型详情
    const poolType = await prisma.poolType.findUnique({
      where: { id },
      include: {
        pools: {
          include: {
            _count: {
              select: {
                accounts: true
              }
            }
          },
          orderBy: {
            name: 'asc'
          }
        }
      }
    })

    if (!poolType) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Pool type not found'
      })
    }

    // 计算统计信息
    let totalAccounts = 0
    let availableAccounts = 0
    let inUseAccounts = 0
    let invalidAccounts = 0
    let expiredAccounts = 0

    for (const pool of poolType.pools) {
      const accountStats = await prisma.account.groupBy({
        by: ['status'],
        where: {
          poolId: pool.id
        },
        _count: {
          status: true
        }
      })

      accountStats.forEach(stat => {
        totalAccounts += stat._count.status
        switch (stat.status) {
          case 'Available':
            availableAccounts += stat._count.status
            break
          case 'InUse':
            inUseAccounts += stat._count.status
            break
          case 'Invalid':
            invalidAccounts += stat._count.status
            break
          case 'Expired':
            expiredAccounts += stat._count.status
            break
        }
      })
    }

    const result = {
      id: poolType.id,
      name: poolType.name,
      pools: poolType.pools.map(pool => ({
        id: pool.id,
        name: pool.name,
        accountCount: pool._count.accounts
      })),
      stats: {
        totalPools: poolType.pools.length,
        totalAccounts,
        availableAccounts,
        inUseAccounts,
        invalidAccounts,
        expiredAccounts
      }
    }

    return {
      success: true,
      data: result
    }
  } catch (error) {
    console.error('Failed to get pool type:', error)
    
    // 如果是已知的错误（如验证错误），直接抛出
    if (error.statusCode) {
      throw error
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to get pool type'
    })
  }
})
