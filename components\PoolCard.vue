<template>
  <UiCard
    :clickable="true"
    hoverable
    @click="handleClick"
  >
    <template #header>
      <div class="flex items-start justify-between">
        <div class="flex-1">
          <h3 class="text-lg font-semibold text-gray-900">{{ pool.name }}</h3>
          <p class="text-sm text-gray-600 mt-1">{{ pool.poolType?.name || '未知类型' }}</p>
        </div>
        
        <div class="flex items-center gap-2">
          <span
            :class="getStatusClasses()"
            class="inline-flex px-2 py-1 text-xs font-medium rounded-full"
          >
            {{ getStatusText() }}
          </span>
        </div>
      </div>
    </template>

    <div class="space-y-4">
      <!-- 统计信息 -->
      <div class="grid grid-cols-2 gap-4">
        <div class="text-center p-3 bg-gray-50 rounded-lg">
          <div class="text-2xl font-bold text-gray-900">{{ stats.total }}</div>
          <div class="text-sm text-gray-600">总账号</div>
        </div>
        
        <div class="text-center p-3 bg-green-50 rounded-lg">
          <div class="text-2xl font-bold text-green-600">{{ stats.available }}</div>
          <div class="text-sm text-gray-600">可用</div>
        </div>
      </div>

      <!-- 详细统计 -->
      <div class="grid grid-cols-3 gap-2 text-center">
        <div>
          <div class="text-lg font-semibold text-blue-600">{{ stats.inUse }}</div>
          <div class="text-xs text-gray-500">占用中</div>
        </div>
        
        <div>
          <div class="text-lg font-semibold text-red-600">{{ stats.invalid }}</div>
          <div class="text-xs text-gray-500">失效</div>
        </div>
        
        <div>
          <div class="text-lg font-semibold text-gray-600">{{ stats.expired }}</div>
          <div class="text-xs text-gray-500">过期</div>
        </div>
      </div>

      <!-- 使用率进度条 -->
      <div>
        <div class="flex justify-between text-sm text-gray-600 mb-1">
          <span>使用率</span>
          <span>{{ usagePercentage }}%</span>
        </div>
        <div class="w-full bg-gray-200 rounded-full h-2">
          <div
            :class="getUsageBarClasses()"
            class="h-2 rounded-full transition-all duration-300"
            :style="{ width: `${usagePercentage}%` }"
          ></div>
        </div>
      </div>

      <!-- 最近活动 -->
      <div v-if="pool.updatedAt" class="text-xs text-gray-500">
        最后更新：{{ formatDate(pool.updatedAt, 'relative') }}
      </div>
    </div>

    <template #actions>
      <div class="flex items-center gap-2">
        <UiButton
          size="xs"
          variant="ghost"
          @click.stop="handleEdit"
        >
          <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
          </svg>
        </UiButton>
        
        <UiButton
          size="xs"
          variant="ghost"
          @click.stop="handleDelete"
        >
          <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
          </svg>
        </UiButton>
      </div>
    </template>
  </UiCard>
</template>

<script setup lang="ts">
import type { Pool, PoolStats } from '~/types'

interface Props {
  pool: Pool & { stats?: PoolStats }
}

interface Emits {
  click: [pool: Pool]
  edit: [pool: Pool]
  delete: [pool: Pool]
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 计算统计信息
const stats = computed(() => {
  return props.pool.stats || {
    total: 0,
    available: 0,
    inUse: 0,
    invalid: 0,
    expired: 0
  }
})

// 计算使用率
const usagePercentage = computed(() => {
  if (stats.value.total === 0) return 0
  return Math.round((stats.value.inUse / stats.value.total) * 100)
})

// 获取状态样式
const getStatusClasses = () => {
  if (stats.value.total === 0) {
    return 'bg-gray-100 text-gray-800'
  }
  
  if (stats.value.available === 0) {
    return 'bg-red-100 text-red-800'
  }
  
  if (usagePercentage.value > 80) {
    return 'bg-yellow-100 text-yellow-800'
  }
  
  return 'bg-green-100 text-green-800'
}

// 获取状态文本
const getStatusText = () => {
  if (stats.value.total === 0) {
    return '空池'
  }
  
  if (stats.value.available === 0) {
    return '无可用'
  }
  
  if (usagePercentage.value > 80) {
    return '使用率高'
  }
  
  return '正常'
}

// 获取使用率进度条样式
const getUsageBarClasses = () => {
  if (usagePercentage.value > 80) {
    return 'bg-red-500'
  } else if (usagePercentage.value > 60) {
    return 'bg-yellow-500'
  } else {
    return 'bg-green-500'
  }
}

// 事件处理
const handleClick = () => {
  emit('click', props.pool)
}

const handleEdit = () => {
  emit('edit', props.pool)
}

const handleDelete = () => {
  emit('delete', props.pool)
}
</script>
