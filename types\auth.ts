/**
 * 认证和权限相关的类型定义
 */

// 权限枚举
export enum Permission {
  // 账号相关权限
  ACCOUNT_READ = 'account:read',
  ACCOUNT_CREATE = 'account:create',
  ACCOUNT_UPDATE = 'account:update',
  ACCOUNT_DELETE = 'account:delete',
  ACCOUNT_BATCH = 'account:batch',
  
  // 号池相关权限
  POOL_READ = 'pool:read',
  POOL_CREATE = 'pool:create',
  POOL_UPDATE = 'pool:update',
  POOL_DELETE = 'pool:delete',
  
  // 统计相关权限
  STATS_READ = 'stats:read',
  
  // 管理相关权限
  ADMIN_MANAGE = 'admin:manage',
  USER_MANAGE = 'user:manage',
  APIKEY_MANAGE = 'apikey:manage'
}

// 用户角色
export enum UserRole {
  ADMIN = 'admin',
  USER = 'user',
  PREMIUM_USER = 'premium_user'
}

// 角色权限映射
export const RolePermissions: Record<UserRole, Permission[]> = {
  [UserRole.ADMIN]: Object.values(Permission), // 管理员拥有所有权限
  
  [UserRole.USER]: [
    Permission.ACCOUNT_READ,
    Permission.POOL_READ,
    Permission.STATS_READ
  ], // 普通用户只有读取权限
  
  [UserRole.PREMIUM_USER]: [
    Permission.ACCOUNT_READ,
    Permission.ACCOUNT_CREATE,
    Permission.ACCOUNT_UPDATE,
    Permission.POOL_READ,
    Permission.STATS_READ
  ] // 高级用户有部分写权限
}

// 管理员信息
export interface AdminInfo {
  id: string
  username: string
  email?: string
  lastLoginAt?: Date
}

// 用户信息
export interface UserInfo {
  id: string
  name: string
  email?: string
  status: string
  permissions: Permission[]
}

// 会话信息
export interface SessionInfo {
  id: string
  adminId: string
  token: string
  expiresAt: Date
  admin: AdminInfo
}

// API密钥信息
export interface ApiKeyInfo {
  id: number
  key: string
  name: string
  description?: string
  status: string
  permissions: Permission[]
  userId?: string
  user?: UserInfo
  expiresAt?: Date
  lastUsedAt?: Date
}

// 登录请求
export interface LoginRequest {
  username: string
  password: string
}

// 登录响应
export interface LoginResponse {
  success: boolean
  admin?: AdminInfo
  token?: string
  message?: string
}

// 权限检查结果
export interface PermissionCheckResult {
  hasPermission: boolean
  missingPermissions?: Permission[]
}
