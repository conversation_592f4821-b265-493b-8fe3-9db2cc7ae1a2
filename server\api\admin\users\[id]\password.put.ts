import { z } from 'zod'
import { prisma } from '~/server/utils/db'
import { requireAdminAuth, hashPassword } from '~/server/utils/auth'

// 验证设置密码请求的schema
const setPasswordSchema = z.object({
  password: z.string().min(6, '密码长度至少6位')
})

export default defineEventHandler(async (event) => {
  try {
    // 验证管理员权限
    await requireAdminAuth(event)

    // 获取用户ID
    const userId = getRouterParam(event, 'id')
    if (!userId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'User ID is required'
      })
    }

    // 验证请求体
    const body = await readBody(event)
    const { password } = setPasswordSchema.parse(body)

    // 查找用户
    const user = await prisma.user.findUnique({
      where: { id: userId }
    })

    if (!user) {
      throw createError({
        statusCode: 404,
        statusMessage: 'User not found'
      })
    }

    // 生成密码哈希
    const passwordHash = await hashPassword(password)

    // 更新用户密码
    await prisma.user.update({
      where: { id: userId },
      data: { passwordHash }
    })

    return {
      success: true,
      message: '密码设置成功'
    }

  } catch (error) {
    console.error('Set user password error:', error)
    
    if (error.name === 'ZodError') {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid request data',
        data: { message: error.errors[0]?.message || '请求数据无效' }
      })
    }

    if (error.statusCode) {
      throw error
    }

    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to set user password'
    })
  }
})
