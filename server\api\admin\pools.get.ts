/**
 * 获取所有号池接口
 * GET /api/admin/pools
 * 支持按号池类型分组查询和统计信息
 */

import { prisma } from '../../utils/db'

export default defineEventHandler(async (event) => {
  try {
    // 获取查询参数
    const query = getQuery(event)
    const { poolTypeId, groupByType } = query

    // 如果指定了按类型分组
    if (groupByType === 'true') {
      return await getPoolsGroupedByType(poolTypeId as string)
    }

    // 构建查询条件
    const whereCondition: any = {}
    if (poolTypeId) {
      whereCondition.poolTypeId = poolTypeId as string
    }

    // 获取所有号池，包含关联信息
    const pools = await prisma.pool.findMany({
      where: whereCondition,
      include: {
        poolType: true,
        _count: {
          select: {
            accounts: true
          }
        }
      },
      orderBy: [
        { poolType: { name: 'asc' } },
        { name: 'asc' }
      ]
    })

    // 为每个号池计算详细统计信息
    const poolsWithStats = await Promise.all(
      pools.map(async (pool) => {
        const accountStats = await prisma.account.groupBy({
          by: ['status'],
          where: {
            poolId: pool.id
          },
          _count: {
            status: true
          }
        })

        const stats = {
          total: 0,
          available: 0,
          inUse: 0,
          invalid: 0,
          expired: 0
        }

        accountStats.forEach(stat => {
          stats.total += stat._count.status
          switch (stat.status) {
            case 'Available':
              stats.available = stat._count.status
              break
            case 'InUse':
              stats.inUse = stat._count.status
              break
            case 'Invalid':
              stats.invalid = stat._count.status
              break
            case 'Expired':
              stats.expired = stat._count.status
              break
          }
        })

        return {
          id: pool.id,
          name: pool.name,
          poolType: {
            id: pool.poolType.id,
            name: pool.poolType.name
          },
          stats,
          createdAt: pool.createdAt || new Date(),
          updatedAt: pool.updatedAt || new Date()
        }
      })
    )

    return {
      success: true,
      data: poolsWithStats,
      total: poolsWithStats.length,
      filters: {
        poolTypeId: poolTypeId || null,
        groupByType: false
      }
    }
  } catch (error) {
    console.error('Failed to get pools:', error)
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to get pools'
    })
  }
})

/**
 * 按号池类型分组获取号池
 */
async function getPoolsGroupedByType(poolTypeId?: string) {
  try {
    // 构建查询条件
    const whereCondition: any = {}
    if (poolTypeId) {
      whereCondition.id = poolTypeId
    }

    // 获取号池类型及其关联的号池
    const poolTypes = await prisma.poolType.findMany({
      where: whereCondition,
      include: {
        pools: {
          include: {
            _count: {
              select: {
                accounts: true
              }
            }
          },
          orderBy: {
            name: 'asc'
          }
        }
      },
      orderBy: {
        name: 'asc'
      }
    })

    // 为每个号池类型计算统计信息
    const groupedData = await Promise.all(
      poolTypes.map(async (poolType) => {
        const poolsWithStats = await Promise.all(
          poolType.pools.map(async (pool) => {
            const accountStats = await prisma.account.groupBy({
              by: ['status'],
              where: {
                poolId: pool.id
              },
              _count: {
                status: true
              }
            })

            const stats = {
              total: 0,
              available: 0,
              inUse: 0,
              invalid: 0,
              expired: 0
            }

            accountStats.forEach(stat => {
              stats.total += stat._count.status
              switch (stat.status) {
                case 'Available':
                  stats.available = stat._count.status
                  break
                case 'InUse':
                  stats.inUse = stat._count.status
                  break
                case 'Invalid':
                  stats.invalid = stat._count.status
                  break
                case 'Expired':
                  stats.expired = stat._count.status
                  break
              }
            })

            return {
              id: pool.id,
              name: pool.name,
              stats
            }
          })
        )

        // 计算号池类型的总统计信息
        const typeStats = poolsWithStats.reduce((acc, pool) => ({
          totalPools: acc.totalPools + 1,
          totalAccounts: acc.totalAccounts + pool.stats.total,
          availableAccounts: acc.availableAccounts + pool.stats.available,
          inUseAccounts: acc.inUseAccounts + pool.stats.inUse,
          invalidAccounts: acc.invalidAccounts + pool.stats.invalid,
          expiredAccounts: acc.expiredAccounts + pool.stats.expired
        }), {
          totalPools: 0,
          totalAccounts: 0,
          availableAccounts: 0,
          inUseAccounts: 0,
          invalidAccounts: 0,
          expiredAccounts: 0
        })

        return {
          id: poolType.id,
          name: poolType.name,
          pools: poolsWithStats,
          stats: typeStats
        }
      })
    )

    return {
      success: true,
      data: groupedData,
      total: groupedData.reduce((acc, type) => acc + type.pools.length, 0),
      filters: {
        poolTypeId: poolTypeId || null,
        groupByType: true
      }
    }
  } catch (error) {
    console.error('Failed to get pools grouped by type:', error)
    throw error
  }
}
