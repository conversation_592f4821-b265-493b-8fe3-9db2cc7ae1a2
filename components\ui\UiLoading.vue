<template>
  <div :class="containerClasses">
    <div :class="spinnerClasses">
      <svg class="animate-spin" fill="none" viewBox="0 0 24 24">
        <circle
          class="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          stroke-width="4"
        ></circle>
        <path
          class="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
        ></path>
      </svg>
    </div>
    
    <p v-if="text" :class="textClasses">{{ text }}</p>
  </div>
</template>

<script setup lang="ts">
interface Props {
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  text?: string
  overlay?: boolean
  color?: 'primary' | 'secondary' | 'white'
}

const props = withDefaults(defineProps<Props>(), {
  size: 'md',
  overlay: false,
  color: 'primary'
})

const containerClasses = computed(() => {
  const baseClasses = ['flex flex-col items-center justify-center']
  
  if (props.overlay) {
    baseClasses.push(
      'fixed inset-0 z-50 bg-white bg-opacity-75',
      'backdrop-blur-sm'
    )
  }
  
  return baseClasses.join(' ')
})

const spinnerClasses = computed(() => {
  const sizeClasses = {
    xs: 'h-4 w-4',
    sm: 'h-6 w-6',
    md: 'h-8 w-8',
    lg: 'h-12 w-12',
    xl: 'h-16 w-16'
  }
  
  const colorClasses = {
    primary: 'text-indigo-600',
    secondary: 'text-gray-600',
    white: 'text-white'
  }
  
  return [
    sizeClasses[props.size],
    colorClasses[props.color]
  ].join(' ')
})

const textClasses = computed(() => {
  const baseClasses = ['mt-2 text-sm font-medium']
  
  const colorClasses = {
    primary: 'text-indigo-600',
    secondary: 'text-gray-600',
    white: 'text-white'
  }
  
  return [
    ...baseClasses,
    colorClasses[props.color]
  ].join(' ')
})
</script>
