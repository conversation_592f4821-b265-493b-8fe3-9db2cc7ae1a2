/**
 * 通知功能组合式函数
 * 提供统一的通知管理功能
 */

import type { NotificationType } from '~/stores/ui'

export interface NotificationOptions {
  title: string
  message?: string
  duration?: number
  persistent?: boolean
  actions?: Array<{
    label: string
    action: () => void
    variant?: 'primary' | 'secondary'
  }>
}

export function useNotification() {
  const uiStore = useUiStore()

  /**
   * 显示通知
   */
  const notify = (type: NotificationType, options: NotificationOptions) => {
    return uiStore.addNotification({
      type,
      ...options
    })
  }

  /**
   * 显示成功通知
   */
  const success = (title: string, message?: string, duration?: number) => {
    return notify('success', { title, message, duration })
  }

  /**
   * 显示错误通知
   */
  const error = (title: string, message?: string, persistent = false) => {
    return notify('error', { 
      title, 
      message, 
      persistent,
      duration: persistent ? 0 : 8000
    })
  }

  /**
   * 显示警告通知
   */
  const warning = (title: string, message?: string, duration?: number) => {
    return notify('warning', { title, message, duration })
  }

  /**
   * 显示信息通知
   */
  const info = (title: string, message?: string, duration?: number) => {
    return notify('info', { title, message, duration })
  }

  /**
   * 显示确认对话框
   */
  const confirm = (
    title: string,
    message?: string,
    options: {
      confirmText?: string
      cancelText?: string
      variant?: 'danger' | 'warning' | 'info'
    } = {}
  ): Promise<boolean> => {
    return new Promise((resolve) => {
      const {
        confirmText = '确认',
        cancelText = '取消',
        variant = 'warning'
      } = options

      const notificationId = notify(variant, {
        title,
        message,
        persistent: true,
        actions: [
          {
            label: confirmText,
            action: () => {
              uiStore.removeNotification(notificationId)
              resolve(true)
            },
            variant: 'primary'
          },
          {
            label: cancelText,
            action: () => {
              uiStore.removeNotification(notificationId)
              resolve(false)
            },
            variant: 'secondary'
          }
        ]
      })
    })
  }

  /**
   * 显示加载通知
   */
  const loading = (title: string, message?: string) => {
    return notify('info', {
      title,
      message,
      persistent: true
    })
  }

  /**
   * 移除通知
   */
  const remove = (id: string) => {
    uiStore.removeNotification(id)
  }

  /**
   * 清除所有通知
   */
  const clear = () => {
    uiStore.clearNotifications()
  }

  /**
   * 处理API错误
   */
  const handleApiError = (err: any, defaultMessage = '操作失败') => {
    const message = err?.message || err?.statusMessage || defaultMessage
    error('错误', message)
  }

  /**
   * 处理API成功
   */
  const handleApiSuccess = (message = '操作成功', details?: string) => {
    success(message, details)
  }

  /**
   * 显示操作结果
   */
  const showResult = async <T>(
    promise: Promise<T>,
    options: {
      loadingMessage?: string
      successMessage?: string
      errorMessage?: string
      showSuccess?: boolean
    } = {}
  ): Promise<T> => {
    const {
      loadingMessage = '处理中...',
      successMessage = '操作成功',
      errorMessage = '操作失败',
      showSuccess = true
    } = options

    let loadingId: string | null = null

    try {
      // 显示加载通知
      if (loadingMessage) {
        loadingId = loading('请稍候', loadingMessage)
      }

      // 执行操作
      const result = await promise

      // 移除加载通知
      if (loadingId) {
        remove(loadingId)
      }

      // 显示成功通知
      if (showSuccess) {
        success(successMessage)
      }

      return result
    } catch (err: any) {
      // 移除加载通知
      if (loadingId) {
        remove(loadingId)
      }

      // 显示错误通知
      handleApiError(err, errorMessage)
      throw err
    }
  }

  return {
    // 基础通知方法
    notify,
    success,
    error,
    warning,
    info,
    
    // 高级功能
    confirm,
    loading,
    remove,
    clear,
    
    // 工具方法
    handleApiError,
    handleApiSuccess,
    showResult
  }
}
