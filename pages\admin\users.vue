<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 页面头部 -->
    <div class="bg-white shadow">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="py-6">
          <!-- 面包屑导航 -->
          <nav class="flex items-center space-x-2 text-sm">
            <NuxtLink to="/" class="text-gray-500 hover:text-gray-700">首页</NuxtLink>
            <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
            <span class="text-gray-900 font-medium">用户管理</span>
          </nav>
        </div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- 统计信息 -->
      <div class="bg-white rounded-lg shadow p-6 mb-8">
        <h2 class="text-lg font-medium text-gray-900 mb-6">统计概览</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div class="text-center p-4 bg-blue-50 rounded-lg">
            <div class="text-2xl font-bold text-blue-600">{{ stats.total }}</div>
            <div class="text-sm text-gray-600">总用户数</div>
          </div>
          <div class="text-center p-4 bg-green-50 rounded-lg">
            <div class="text-2xl font-bold text-green-600">{{ stats.active }}</div>
            <div class="text-sm text-gray-600">活跃用户</div>
          </div>
          <div class="text-center p-4 bg-gray-50 rounded-lg">
            <div class="text-2xl font-bold text-gray-600">{{ stats.disabled }}</div>
            <div class="text-sm text-gray-600">已禁用</div>
          </div>
          <div class="text-center p-4 bg-purple-50 rounded-lg">
            <div class="text-2xl font-bold text-purple-600">{{ stats.totalApiKeys }}</div>
            <div class="text-sm text-gray-600">API密钥总数</div>
          </div>
        </div>
      </div>

      <!-- 用户列表 -->
      <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
          <div class="flex items-center justify-between">
            <h2 class="text-lg font-medium text-gray-900">用户列表</h2>
            <div class="flex items-center space-x-3">
              <!-- 搜索框 -->
              <div class="relative">
                <input
                  v-model="searchQuery"
                  type="text"
                  placeholder="搜索用户..."
                  class="w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  @input="debouncedSearch"
                />
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
              </div>

              <!-- 状态筛选 -->
              <select
                v-model="statusFilter"
                class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                @change="loadUsers"
              >
                <option value="">所有状态</option>
                <option value="Active">活跃</option>
                <option value="Disabled">已禁用</option>
              </select>

              <!-- 刷新按钮 -->
              <button
                @click="loadUsers"
                :disabled="loading"
                class="flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50"
              >
                <svg
                  :class="{ 'animate-spin': loading }"
                  class="w-4 h-4 mr-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                刷新
              </button>

              <!-- 创建用户按钮 -->
              <button
                @click="showCreateModal = true"
                class="flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700"
              >
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                创建用户
              </button>
            </div>
          </div>
        </div>

        <!-- 用户表格 -->
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">用户信息</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">API密钥</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建时间</th>
                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="user in users" :key="user.id" class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div>
                    <div class="text-sm font-medium text-gray-900">{{ user.name }}</div>
                    <div v-if="user.email" class="text-sm text-gray-500">{{ user.email }}</div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span
                    :class="{
                      'bg-green-100 text-green-800': user.status === 'Active',
                      'bg-red-100 text-red-800': user.status === 'Disabled'
                    }"
                    class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                  >
                    {{ user.status === 'Active' ? '活跃' : '已禁用' }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {{ user.apiKeys?.length || 0 }} 个
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ formatDate(user.createdAt) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <div class="flex items-center justify-end space-x-2">
                    <button
                      @click="editUser(user)"
                      class="text-blue-600 hover:text-blue-900"
                    >
                      编辑
                    </button>
                    <button
                      @click="setUserPassword(user)"
                      class="text-purple-600 hover:text-purple-900"
                    >
                      设置密码
                    </button>
                    <button
                      @click="toggleUserStatus(user)"
                      :class="{
                        'text-red-600 hover:text-red-900': user.status === 'Active',
                        'text-green-600 hover:text-green-900': user.status === 'Disabled'
                      }"
                    >
                      {{ user.status === 'Active' ? '禁用' : '启用' }}
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- 分页 -->
        <div v-if="pagination.totalPages > 1" class="px-6 py-4 border-t border-gray-200">
          <div class="flex items-center justify-between">
            <div class="text-sm text-gray-700">
              显示第 {{ (pagination.page - 1) * pagination.limit + 1 }} - 
              {{ Math.min(pagination.page * pagination.limit, pagination.total) }} 条，
              共 {{ pagination.total }} 条记录
            </div>
            <div class="flex items-center space-x-2">
              <button
                @click="changePage(pagination.page - 1)"
                :disabled="!pagination.hasPrev"
                class="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                上一页
              </button>
              <span class="px-3 py-1 text-sm">
                第 {{ pagination.page }} / {{ pagination.totalPages }} 页
              </span>
              <button
                @click="changePage(pagination.page + 1)"
                :disabled="!pagination.hasNext"
                class="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                下一页
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 创建用户模态框 -->
    <div v-if="showCreateModal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
        <h3 class="text-lg font-medium text-gray-900 mb-4">创建用户</h3>
        <form @submit.prevent="handleCreateUser">
          <div class="mb-4">
            <label for="userName" class="block text-sm font-medium text-gray-700 mb-2">
              用户名称 *
            </label>
            <input
              id="userName"
              v-model="userForm.name"
              type="text"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="请输入用户名称"
            />
          </div>
          <div class="mb-4">
            <label for="userEmail" class="block text-sm font-medium text-gray-700 mb-2">
              邮箱地址
            </label>
            <input
              id="userEmail"
              v-model="userForm.email"
              type="email"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="请输入邮箱地址（可选）"
            />
          </div>
          <div class="flex gap-3">
            <button
              type="button"
              @click="closeCreateModal"
              class="flex-1 bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400"
            >
              取消
            </button>
            <button
              type="submit"
              :disabled="!userForm.name.trim() || creating"
              class="flex-1 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {{ creating ? '创建中...' : '创建' }}
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- 编辑用户模态框 -->
    <div v-if="showEditModal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
        <h3 class="text-lg font-medium text-gray-900 mb-4">编辑用户</h3>
        <form @submit.prevent="handleEditUser">
          <div class="mb-4">
            <label for="editUserName" class="block text-sm font-medium text-gray-700 mb-2">
              用户名称 *
            </label>
            <input
              id="editUserName"
              v-model="editUserForm.name"
              type="text"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="请输入用户名称"
            />
          </div>
          <div class="mb-4">
            <label for="editUserEmail" class="block text-sm font-medium text-gray-700 mb-2">
              邮箱地址
            </label>
            <input
              id="editUserEmail"
              v-model="editUserForm.email"
              type="email"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="请输入邮箱地址（可选）"
            />
          </div>
          <div class="mb-4">
            <label for="editUserStatus" class="block text-sm font-medium text-gray-700 mb-2">
              用户状态
            </label>
            <select
              id="editUserStatus"
              v-model="editUserForm.status"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="Active">活跃</option>
              <option value="Disabled">已禁用</option>
            </select>
          </div>
          <div class="flex gap-3">
            <button
              type="button"
              @click="closeEditModal"
              class="flex-1 bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400"
            >
              取消
            </button>
            <button
              type="submit"
              :disabled="!editUserForm.name.trim() || editing"
              class="flex-1 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {{ editing ? '保存中...' : '保存' }}
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- 设置密码模态框 -->
    <div v-if="showPasswordModal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
        <h3 class="text-lg font-medium text-gray-900 mb-4">
          设置用户密码 - {{ settingPasswordUser?.name }}
        </h3>
        <form @submit.prevent="handleSetPassword">
          <div class="mb-4">
            <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
              新密码 *
            </label>
            <input
              id="password"
              v-model="passwordForm.password"
              type="password"
              required
              minlength="6"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
              placeholder="请输入新密码（至少6位）"
            />
          </div>
          <div class="mb-4">
            <label for="confirmPassword" class="block text-sm font-medium text-gray-700 mb-2">
              确认密码 *
            </label>
            <input
              id="confirmPassword"
              v-model="passwordForm.confirmPassword"
              type="password"
              required
              minlength="6"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
              placeholder="请再次输入密码"
            />
          </div>
          <div class="mb-4 text-sm text-gray-600">
            <p>• 密码长度至少6位</p>
            <p>• 设置后用户可以使用邮箱和密码登录</p>
          </div>
          <div class="flex gap-3">
            <button
              type="button"
              @click="showPasswordModal = false"
              class="flex-1 bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400"
            >
              取消
            </button>
            <button
              type="submit"
              :disabled="!passwordForm.password.trim() || !passwordForm.confirmPassword.trim()"
              class="flex-1 bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              设置密码
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 页面标题
useHead({
  title: '用户管理 - EasyPool'
})

// 响应式数据
const loading = ref(true)
const creating = ref(false)
const editing = ref(false)
const showCreateModal = ref(false)
const showEditModal = ref(false)
const showPasswordModal = ref(false)
const searchQuery = ref('')
const statusFilter = ref('')
const editingUser = ref(null)
const settingPasswordUser = ref(null)

// 用户数据
const users = ref([])
const stats = ref({
  total: 0,
  active: 0,
  disabled: 0,
  totalApiKeys: 0
})

const pagination = ref({
  page: 1,
  limit: 20,
  total: 0,
  totalPages: 0,
  hasNext: false,
  hasPrev: false
})

// 表单数据
const userForm = ref({
  name: '',
  email: ''
})

const editUserForm = ref({
  id: '',
  name: '',
  email: '',
  status: 'Active'
})

const passwordForm = ref({
  password: '',
  confirmPassword: ''
})

// 加载用户列表
const loadUsers = async (page = 1) => {
  try {
    loading.value = true
    
    const params = new URLSearchParams({
      page: page.toString(),
      limit: pagination.value.limit.toString()
    })
    
    if (searchQuery.value.trim()) {
      params.append('search', searchQuery.value.trim())
    }
    
    if (statusFilter.value) {
      params.append('status', statusFilter.value)
    }

    const response = await $fetch(`/api/admin/users?${params}`)
    
    if (response.success) {
      users.value = response.data.users
      pagination.value = response.data.pagination
      
      // 计算统计信息
      stats.value = {
        total: response.data.pagination.total,
        active: response.data.users.filter(u => u.status === 'Active').length,
        disabled: response.data.users.filter(u => u.status === 'Disabled').length,
        totalApiKeys: response.data.users.reduce((sum, u) => sum + (u.apiKeys?.length || 0), 0)
      }
    }
  } catch (error) {
    console.error('Failed to load users:', error)
  } finally {
    loading.value = false
  }
}

// 防抖搜索
const debouncedSearch = debounce(() => {
  loadUsers(1)
}, 500)

// 创建用户
const handleCreateUser = async () => {
  if (!userForm.value.name.trim()) return

  try {
    creating.value = true
    
    const response = await $fetch('/api/admin/users', {
      method: 'POST',
      body: {
        name: userForm.value.name.trim(),
        email: userForm.value.email.trim() || undefined
      }
    })

    if (response.success) {
      await loadUsers()
      closeCreateModal()
      console.log('用户创建成功:', response.data.name)
    }
  } catch (error) {
    console.error('创建用户失败:', error)
  } finally {
    creating.value = false
  }
}

// 关闭创建模态框
const closeCreateModal = () => {
  showCreateModal.value = false
  userForm.value = {
    name: '',
    email: ''
  }
}

// 编辑用户
const editUser = (user) => {
  editingUser.value = user
  editUserForm.value = {
    id: user.id,
    name: user.name,
    email: user.email || '',
    status: user.status
  }
  showEditModal.value = true
}

// 处理编辑用户
const handleEditUser = async () => {
  if (!editUserForm.value.name.trim()) return

  try {
    editing.value = true

    const response = await $fetch(`/api/admin/users/${editUserForm.value.id}`, {
      method: 'PUT',
      body: {
        name: editUserForm.value.name.trim(),
        email: editUserForm.value.email.trim() || null,
        status: editUserForm.value.status
      }
    })

    if (response.success) {
      await loadUsers()
      closeEditModal()
      console.log('用户更新成功:', response.data.name)
    }
  } catch (error) {
    console.error('更新用户失败:', error)
  } finally {
    editing.value = false
  }
}

// 关闭编辑模态框
const closeEditModal = () => {
  showEditModal.value = false
  editingUser.value = null
  editUserForm.value = {
    id: '',
    name: '',
    email: '',
    status: 'Active'
  }
}

// 切换用户状态
const toggleUserStatus = async (user) => {
  const newStatus = user.status === 'Active' ? 'Disabled' : 'Active'
  const action = newStatus === 'Active' ? '启用' : '禁用'

  if (!confirm(`确定要${action}用户 "${user.name}" 吗？`)) return

  try {
    const response = await $fetch(`/api/admin/users/${user.id}`, {
      method: 'PUT',
      body: {
        status: newStatus
      }
    })

    if (response.success) {
      await loadUsers()
      console.log(`${action}用户成功:`, user.name)
    }
  } catch (error) {
    console.error(`${action}用户失败:`, error)
  }
}

// 设置用户密码
const setUserPassword = (user) => {
  settingPasswordUser.value = user
  passwordForm.value = {
    password: '',
    confirmPassword: ''
  }
  showPasswordModal.value = true
}

const handleSetPassword = async () => {
  if (passwordForm.value.password !== passwordForm.value.confirmPassword) {
    alert('两次输入的密码不一致')
    return
  }

  if (passwordForm.value.password.length < 6) {
    alert('密码长度至少6位')
    return
  }

  try {
    const response = await $fetch(`/api/admin/users/${settingPasswordUser.value.id}/password`, {
      method: 'PUT',
      body: {
        password: passwordForm.value.password
      }
    })

    if (response.success) {
      alert('密码设置成功')
      showPasswordModal.value = false
      settingPasswordUser.value = null
      passwordForm.value = {
        password: '',
        confirmPassword: ''
      }
    } else {
      alert(response.message || '设置密码失败')
    }
  } catch (error) {
    console.error('Set password error:', error)
    alert('设置密码失败，请重试')
  }
}

// 分页
const changePage = (page) => {
  if (page >= 1 && page <= pagination.value.totalPages) {
    loadUsers(page)
  }
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '未知'
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN')
}

// 防抖函数
function debounce(func, wait) {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

// 生命周期
onMounted(() => {
  loadUsers()
})
</script>
