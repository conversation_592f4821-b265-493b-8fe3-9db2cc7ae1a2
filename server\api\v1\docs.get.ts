/**
 * API文档接口
 * GET /api/v1/docs
 * 
 * 提供API使用说明，不需要API密钥认证
 */

export default defineEventHandler(async (event) => {
  const baseUrl = process.env.API_BASE_URL || 'http://localhost:3000'
  
  const apiDocs = {
    name: 'EasyPool API',
    version: '1.0.0',
    description: '简易号池管理系统API接口文档',
    baseUrl: `${baseUrl}/api/v1`,
    authentication: {
      type: 'API Key',
      header: 'X-API-KEY',
      description: '所有API请求（除了/health和/docs）都需要在请求头中包含有效的API密钥'
    },
    endpoints: [
      {
        path: '/account',
        method: 'GET',
        description: '获取一个可用账号（取号）',
        authentication: 'required',
        parameters: [
          {
            name: 'poolName',
            type: 'string',
            required: 'conditional',
            description: '号池名称，与poolType二选一必填'
          },
          {
            name: 'poolType',
            type: 'string',
            required: 'conditional',
            description: '号池类型名称，与poolName二选一必填'
          }
        ],
        responses: {
          '200': {
            description: '成功获取账号',
            example: {
              success: true,
              data: {
                accountId: 'account_id_here',
                content: 'account_content_here',
                poolName: 'pool_name_here',
                poolType: 'pool_type_here',
                expiresAt: '2023-12-31T23:59:59Z',
                retrievedAt: '2023-01-01T12:00:00Z',
                notes: 'Optional notes'
              }
            }
          },
          '400': { description: '请求参数错误' },
          '401': { description: 'API密钥无效或缺失' },
          '404': { description: '找不到可用账号' },
          '500': { description: '服务器内部错误' }
        }
      },
      {
        path: '/account/report',
        method: 'POST',
        description: '上报账号使用结果（归还）',
        authentication: 'required',
        requestBody: {
          content: 'application/json',
          schema: {
            accountId: { type: 'string', required: true, description: '账号ID' },
            result: { type: 'string', required: true, description: '使用结果，可选值：ok, failed' },
            notes: { type: 'string', required: false, description: '可选的备注信息' }
          },
          example: {
            accountId: 'account_id_here',
            result: 'ok',
            notes: 'Optional notes'
          }
        },
        responses: {
          '200': {
            description: '成功上报账号状态',
            example: {
              success: true,
              message: 'Account status reported successfully',
              data: {
                accountId: 'account_id_here',
                previousStatus: 'InUse',
                newStatus: 'Available',
                result: 'ok',
                poolName: 'pool_name_here',
                poolType: 'pool_type_here',
                reportedAt: '2023-01-01T12:30:00Z',
                wasExpired: false,
                notes: 'Optional notes'
              }
            }
          },
          '400': { description: '请求参数错误或账号状态不是InUse' },
          '401': { description: 'API密钥无效或缺失' },
          '404': { description: '找不到指定账号' },
          '500': { description: '服务器内部错误' }
        }
      },
      {
        path: '/stats',
        method: 'GET',
        description: '获取API使用统计信息',
        authentication: 'required',
        parameters: [
          {
            name: 'poolName',
            type: 'string',
            required: false,
            description: '按号池名称筛选统计'
          },
          {
            name: 'poolType',
            type: 'string',
            required: false,
            description: '按号池类型筛选统计'
          }
        ],
        responses: {
          '200': { description: '成功获取统计信息' },
          '401': { description: 'API密钥无效或缺失' },
          '500': { description: '服务器内部错误' }
        }
      },
      {
        path: '/health',
        method: 'GET',
        description: '健康检查接口',
        authentication: 'none',
        responses: {
          '200': { description: '系统健康状态' }
        }
      },
      {
        path: '/docs',
        method: 'GET',
        description: 'API文档',
        authentication: 'none',
        responses: {
          '200': { description: 'API文档' }
        }
      }
    ],
    errorFormat: {
      description: '错误响应格式',
      example: {
        error: true,
        statusCode: 400,
        statusMessage: 'Error message here',
        message: 'Error message here'
      }
    }
  }

  return {
    success: true,
    data: apiDocs
  }
})
