/**
 * 更新用户接口
 * PUT /api/admin/users/[id]
 */

import { prisma } from '../../../utils/db'

export default defineEventHandler(async (event) => {
  try {
    // 获取路由参数中的ID
    const id = getRouterParam(event, 'id')
    
    if (!id) {
      throw createError({
        statusCode: 400,
        statusMessage: 'User ID is required'
      })
    }

    // 验证ID格式
    if (typeof id !== 'string' || id.length < 10) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid user ID format'
      })
    }

    // 获取请求体数据
    const body = await readBody(event)
    
    // 数据验证
    if (!body || (!body.name && !body.email && !body.status)) {
      throw createError({
        statusCode: 400,
        statusMessage: 'At least one field (name, email, or status) is required for update'
      })
    }

    const { name, email, status } = body
    const updateData: any = {}

    // 验证并处理名称更新
    if (name !== undefined) {
      if (typeof name !== 'string' || name.trim().length === 0) {
        throw createError({
          statusCode: 400,
          statusMessage: 'User name must be a non-empty string'
        })
      }

      const trimmedName = name.trim()

      if (trimmedName.length > 100) {
        throw createError({
          statusCode: 400,
          statusMessage: 'User name must be 100 characters or less'
        })
      }

      updateData.name = trimmedName
    }

    // 验证并处理邮箱更新
    if (email !== undefined) {
      if (email === null || email === '') {
        updateData.email = null
      } else if (typeof email === 'string') {
        const trimmedEmail = email.trim()
        
        if (trimmedEmail.length > 255) {
          throw createError({
            statusCode: 400,
            statusMessage: 'Email must be less than 255 characters'
          })
        }
        
        // 简单的邮箱格式验证
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
        if (!emailRegex.test(trimmedEmail)) {
          throw createError({
            statusCode: 400,
            statusMessage: 'Invalid email format'
          })
        }

        // 检查邮箱是否与其他用户冲突
        const emailConflict = await prisma.user.findFirst({
          where: {
            email: trimmedEmail,
            id: { not: id }
          }
        })

        if (emailConflict) {
          throw createError({
            statusCode: 409,
            statusMessage: 'User with this email already exists'
          })
        }

        updateData.email = trimmedEmail
      } else {
        throw createError({
          statusCode: 400,
          statusMessage: 'Email must be a string or null'
        })
      }
    }

    // 验证并处理状态更新
    if (status !== undefined) {
      if (!['Active', 'Disabled'].includes(status)) {
        throw createError({
          statusCode: 400,
          statusMessage: 'Status must be either "Active" or "Disabled"'
        })
      }

      updateData.status = status
    }

    // 检查用户是否存在
    const existingUser = await prisma.user.findUnique({
      where: { id }
    })

    if (!existingUser) {
      throw createError({
        statusCode: 404,
        statusMessage: 'User not found'
      })
    }

    // 更新用户
    const updatedUser = await prisma.user.update({
      where: { id },
      data: updateData,
      include: {
        apiKeys: {
          select: {
            id: true,
            name: true,
            status: true,
            createdAt: true,
            lastUsedAt: true
          }
        }
      }
    })

    return {
      success: true,
      data: updatedUser,
      message: 'User updated successfully'
    }
  } catch (error) {
    console.error('Update user error:', error)
    
    if (error.statusCode) {
      throw error
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to update user'
    })
  }
})
