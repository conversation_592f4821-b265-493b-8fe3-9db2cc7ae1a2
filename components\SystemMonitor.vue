<template>
  <UiCard title="系统监控" subtitle="实时监控系统健康状态和性能指标">
    <div class="space-y-6">
      <!-- 整体状态 -->
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <div
            :class="[
              'w-3 h-3 rounded-full mr-3',
              healthStatus?.status === 'healthy' ? 'bg-green-500' :
              healthStatus?.status === 'degraded' ? 'bg-yellow-500' : 'bg-red-500'
            ]"
          ></div>
          <div>
            <h3 class="text-lg font-medium text-gray-900">
              系统状态: {{ getStatusText(healthStatus?.status || 'unhealthy') }}
            </h3>
            <p class="text-sm text-gray-500">
              最后检查: {{ lastCheckTime ? formatDate(lastCheckTime, 'relative') : '未检查' }}
            </p>
          </div>
        </div>
        
        <UiButton
          size="sm"
          :loading="isChecking"
          @click="performHealthCheck"
        >
          <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
          刷新
        </UiButton>
      </div>

      <!-- 详细检查结果 -->
      <div v-if="healthStatus" class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <!-- API状态 -->
        <div class="bg-gray-50 rounded-lg p-4">
          <div class="flex items-center justify-between mb-2">
            <h4 class="text-sm font-medium text-gray-900">API服务</h4>
            <span
              :class="[
                'inline-flex px-2 py-1 text-xs font-semibold rounded-full',
                healthStatus.checks.api.status === 'ok' 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-red-100 text-red-800'
              ]"
            >
              {{ healthStatus.checks.api.status === 'ok' ? '正常' : '异常' }}
            </span>
          </div>
          <p class="text-sm text-gray-600">{{ healthStatus.checks.api.message }}</p>
          <div class="mt-2 text-xs text-gray-500">
            响应时间: {{ healthStatus.checks.api.responseTime }}ms
          </div>
        </div>

        <!-- 数据库状态 -->
        <div class="bg-gray-50 rounded-lg p-4">
          <div class="flex items-center justify-between mb-2">
            <h4 class="text-sm font-medium text-gray-900">数据库</h4>
            <span
              :class="[
                'inline-flex px-2 py-1 text-xs font-semibold rounded-full',
                healthStatus.checks.database.status === 'ok' 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-red-100 text-red-800'
              ]"
            >
              {{ healthStatus.checks.database.status === 'ok' ? '正常' : '异常' }}
            </span>
          </div>
          <p class="text-sm text-gray-600">{{ healthStatus.checks.database.message }}</p>
        </div>

        <!-- 内存使用 -->
        <div class="bg-gray-50 rounded-lg p-4">
          <div class="flex items-center justify-between mb-2">
            <h4 class="text-sm font-medium text-gray-900">内存使用</h4>
            <span
              :class="[
                'inline-flex px-2 py-1 text-xs font-semibold rounded-full',
                healthStatus.checks.memory.status === 'ok' ? 'bg-green-100 text-green-800' :
                healthStatus.checks.memory.status === 'warning' ? 'bg-yellow-100 text-yellow-800' :
                'bg-red-100 text-red-800'
              ]"
            >
              {{ 
                healthStatus.checks.memory.status === 'ok' ? '正常' :
                healthStatus.checks.memory.status === 'warning' ? '警告' : '异常'
              }}
            </span>
          </div>
          <p class="text-sm text-gray-600">{{ healthStatus.checks.memory.message }}</p>
          <div class="mt-2">
            <div class="flex justify-between text-xs text-gray-500 mb-1">
              <span>使用率</span>
              <span>{{ healthStatus.checks.memory.usage }}%</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div
                :class="[
                  'h-2 rounded-full transition-all duration-300',
                  healthStatus.checks.memory.usage > 80 ? 'bg-red-500' :
                  healthStatus.checks.memory.usage > 60 ? 'bg-yellow-500' : 'bg-green-500'
                ]"
                :style="{ width: `${Math.min(healthStatus.checks.memory.usage, 100)}%` }"
              ></div>
            </div>
          </div>
        </div>

        <!-- 网络状态 -->
        <div class="bg-gray-50 rounded-lg p-4">
          <div class="flex items-center justify-between mb-2">
            <h4 class="text-sm font-medium text-gray-900">网络连接</h4>
            <span
              :class="[
                'inline-flex px-2 py-1 text-xs font-semibold rounded-full',
                healthStatus.checks.network.status === 'ok' 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-red-100 text-red-800'
              ]"
            >
              {{ healthStatus.checks.network.status === 'ok' ? '正常' : '异常' }}
            </span>
          </div>
          <p class="text-sm text-gray-600">{{ healthStatus.checks.network.message }}</p>
        </div>
      </div>

      <!-- 性能指标 -->
      <div v-if="healthStatus" class="border-t pt-6">
        <h4 class="text-sm font-medium text-gray-900 mb-4">性能指标</h4>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <StatsCard
            title="API响应时间"
            :value="healthStatus.performance.apiResponseTime"
            unit="ms"
            icon="ClockIcon"
            icon-color="blue"
            size="sm"
          />
          
          <StatsCard
            title="内存使用率"
            :value="healthStatus.performance.memoryUsage"
            unit="%"
            icon="CpuChipIcon"
            icon-color="purple"
            size="sm"
          />
          
          <StatsCard
            title="活跃连接"
            :value="healthStatus.performance.activeConnections"
            icon="GlobeAltIcon"
            icon-color="green"
            size="sm"
          />
        </div>
      </div>

      <!-- 系统信息 -->
      <div class="border-t pt-6">
        <h4 class="text-sm font-medium text-gray-900 mb-4">系统信息</h4>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <span class="font-medium text-gray-700">用户代理:</span>
            <span class="text-gray-600 ml-2 break-all">{{ userAgent }}</span>
          </div>
          <div>
            <span class="font-medium text-gray-700">时区:</span>
            <span class="text-gray-600 ml-2">{{ timezone }}</span>
          </div>
          <div>
            <span class="font-medium text-gray-700">语言:</span>
            <span class="text-gray-600 ml-2">{{ language }}</span>
          </div>
          <div>
            <span class="font-medium text-gray-700">屏幕分辨率:</span>
            <span class="text-gray-600 ml-2">{{ screenResolution }}</span>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="border-t pt-6 flex gap-3">
        <UiButton
          size="sm"
          variant="secondary"
          @click="exportHealthReport"
        >
          <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          导出报告
        </UiButton>
        
        <UiButton
          size="sm"
          variant="ghost"
          @click="clearHealthHistory"
        >
          <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
          </svg>
          清除历史
        </UiButton>
      </div>
    </div>
  </UiCard>
</template>

<script setup lang="ts">
const { 
  healthStatus, 
  isChecking, 
  lastCheckTime, 
  performHealthCheck, 
  getStatusText 
} = useHealthCheck()

const { success } = useNotification()

// 系统信息
const userAgent = computed(() => {
  if (process.client) {
    return navigator.userAgent
  }
  return 'Unknown'
})

const timezone = computed(() => {
  if (process.client) {
    return Intl.DateTimeFormat().resolvedOptions().timeZone
  }
  return 'Unknown'
})

const language = computed(() => {
  if (process.client) {
    return navigator.language
  }
  return 'Unknown'
})

const screenResolution = computed(() => {
  if (process.client) {
    return `${screen.width} x ${screen.height}`
  }
  return 'Unknown'
})

// 导出健康报告
const exportHealthReport = () => {
  if (!healthStatus.value) {
    return
  }

  const report = {
    timestamp: new Date().toISOString(),
    systemInfo: {
      userAgent: userAgent.value,
      timezone: timezone.value,
      language: language.value,
      screenResolution: screenResolution.value
    },
    healthStatus: healthStatus.value
  }

  const blob = new Blob([JSON.stringify(report, null, 2)], { 
    type: 'application/json' 
  })
  
  const link = document.createElement('a')
  const url = URL.createObjectURL(blob)
  link.setAttribute('href', url)
  link.setAttribute('download', `health-report-${new Date().toISOString().split('T')[0]}.json`)
  link.style.visibility = 'hidden'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  
  success('导出成功', '健康报告已下载')
}

// 清除健康历史
const clearHealthHistory = () => {
  // 这里可以清除本地存储的健康检查历史
  success('清除成功', '健康检查历史已清除')
}
</script>
