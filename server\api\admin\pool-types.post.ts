/**
 * 创建号池类型接口
 * POST /api/admin/pool-types
 */

import { prisma } from '../../utils/db'

export default defineEventHandler(async (event) => {
  try {
    // 获取请求体数据
    const body = await readBody(event)
    
    // 数据验证
    if (!body || !body.name) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Pool type name is required'
      })
    }

    const { name } = body

    // 验证名称格式
    if (typeof name !== 'string' || name.trim().length === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Pool type name must be a non-empty string'
      })
    }

    const trimmedName = name.trim()

    // 验证名称长度
    if (trimmedName.length > 50) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Pool type name must be 50 characters or less'
      })
    }

    // 检查名称是否已存在
    const existingPoolType = await prisma.poolType.findUnique({
      where: { name: trimmedName }
    })

    if (existingPoolType) {
      throw createError({
        statusCode: 409,
        statusMessage: 'Pool type with this name already exists'
      })
    }

    // 创建新的号池类型
    const newPoolType = await prisma.poolType.create({
      data: {
        name: trimmedName
      }
    })

    console.log(`New pool type created: ${newPoolType.name} (ID: ${newPoolType.id})`)

    return {
      success: true,
      message: 'Pool type created successfully',
      data: {
        id: newPoolType.id,
        name: newPoolType.name,
        stats: {
          totalPools: 0,
          totalAccounts: 0,
          availableAccounts: 0,
          inUseAccounts: 0,
          invalidAccounts: 0,
          expiredAccounts: 0
        }
      }
    }
  } catch (error) {
    console.error('Failed to create pool type:', error)
    
    // 如果是已知的错误（如验证错误），直接抛出
    if (error.statusCode) {
      throw error
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to create pool type'
    })
  }
})
