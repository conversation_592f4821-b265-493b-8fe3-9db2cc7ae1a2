<template>
  <UiModal
    v-model:show="isVisible"
    title="批量导入账号"
    size="lg"
    @close="handleClose"
  >
    <div class="space-y-6">
      <!-- 导入方式选择 -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">
          导入方式
        </label>
        <div class="flex gap-4">
          <label class="flex items-center">
            <input
              v-model="importMode"
              type="radio"
              value="text"
              class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
            />
            <span class="ml-2 text-sm text-gray-700">文本输入</span>
          </label>
          <label class="flex items-center">
            <input
              v-model="importMode"
              type="radio"
              value="file"
              class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
            />
            <span class="ml-2 text-sm text-gray-700">文件上传</span>
          </label>
        </div>
      </div>

      <!-- 文本输入模式 -->
      <div v-if="importMode === 'text'">
        <label class="block text-sm font-medium text-gray-700 mb-2">
          账号列表
          <span class="text-red-500">*</span>
        </label>
        <textarea
          v-model="accountsText"
          class="block w-full h-40 rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm font-mono"
          placeholder="每行一个账号，支持以下格式：&#10;账号内容&#10;账号内容|备注&#10;账号内容|备注|过期时间(YYYY-MM-DD HH:mm:ss)"
          @input="parseAccountsText"
        ></textarea>
        <div class="mt-1 flex justify-between">
          <p v-if="parseError" class="text-sm text-red-600">{{ parseError }}</p>
          <span class="text-xs text-gray-500">
            已识别 {{ parsedAccounts.length }} 个账号
          </span>
        </div>
      </div>

      <!-- 文件上传模式 -->
      <div v-if="importMode === 'file'">
        <label class="block text-sm font-medium text-gray-700 mb-2">
          选择文件
          <span class="text-red-500">*</span>
        </label>
        <div
          class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors"
          @drop="handleFileDrop"
          @dragover.prevent
          @dragenter.prevent
        >
          <input
            ref="fileInput"
            type="file"
            accept=".txt,.csv"
            class="hidden"
            @change="handleFileSelect"
          />
          
          <div v-if="!selectedFile">
            <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
              <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
            </svg>
            <div class="mt-4">
              <button
                type="button"
                class="text-blue-600 hover:text-blue-500 font-medium"
                @click="$refs.fileInput?.click()"
              >
                点击选择文件
              </button>
              <span class="text-gray-500"> 或拖拽文件到此处</span>
            </div>
            <p class="text-xs text-gray-500 mt-2">
              支持 .txt 和 .csv 文件，最大 10MB
            </p>
          </div>
          
          <div v-else class="space-y-2">
            <div class="flex items-center justify-center">
              <svg class="h-8 w-8 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <p class="text-sm font-medium text-gray-900">{{ selectedFile.name }}</p>
            <p class="text-xs text-gray-500">{{ formatFileSize(selectedFile.size) }}</p>
            <button
              type="button"
              class="text-red-600 hover:text-red-500 text-sm"
              @click="clearFile"
            >
              移除文件
            </button>
          </div>
        </div>
        
        <div v-if="fileParseError" class="mt-2">
          <p class="text-sm text-red-600">{{ fileParseError }}</p>
        </div>
      </div>

      <!-- 全局设置 -->
      <div class="border-t pt-4">
        <h4 class="text-sm font-medium text-gray-900 mb-3">全局设置</h4>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <UiInput
            v-model="globalSettings.notes"
            label="统一备注"
            placeholder="为所有账号添加统一备注"
            hint="如果账号本身有备注，将会合并"
          />
          
          <UiInput
            v-model="globalSettings.expiresAt"
            type="datetime-local"
            label="统一过期时间"
            hint="如果账号本身有过期时间，将会覆盖"
          />
        </div>
      </div>

      <!-- 预览 -->
      <div v-if="parsedAccounts.length > 0" class="border-t pt-4">
        <h4 class="text-sm font-medium text-gray-900 mb-3">
          预览 (显示前5条)
        </h4>
        <div class="bg-gray-50 rounded-lg p-3 max-h-40 overflow-y-auto">
          <div
            v-for="(account, index) in parsedAccounts.slice(0, 5)"
            :key="index"
            class="text-xs font-mono text-gray-700 py-1 border-b border-gray-200 last:border-b-0"
          >
            <div><strong>内容:</strong> {{ account.content }}</div>
            <div v-if="account.notes"><strong>备注:</strong> {{ account.notes }}</div>
            <div v-if="account.expiresAt"><strong>过期:</strong> {{ account.expiresAt }}</div>
          </div>
          <div v-if="parsedAccounts.length > 5" class="text-xs text-gray-500 pt-2">
            还有 {{ parsedAccounts.length - 5 }} 个账号...
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <UiButton variant="secondary" @click="handleClose">
        取消
      </UiButton>
      <UiButton
        :loading="submitting"
        :disabled="parsedAccounts.length === 0"
        @click="handleSubmit"
      >
        导入 {{ parsedAccounts.length }} 个账号
      </UiButton>
    </template>
  </UiModal>
</template>

<script setup lang="ts">
import type { BatchImportForm } from '~/types'

interface Props {
  show: boolean
  poolId: string
}

interface Emits {
  'update:show': [value: boolean]
  submit: [data: BatchImportForm]
  close: []
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const isVisible = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

const importMode = ref<'text' | 'file'>('text')
const accountsText = ref('')
const selectedFile = ref<File | null>(null)
const parsedAccounts = ref<Array<{ content: string; notes?: string; expiresAt?: string }>>([])
const parseError = ref('')
const fileParseError = ref('')
const submitting = ref(false)

const globalSettings = ref({
  notes: '',
  expiresAt: ''
})

// 方法
const parseAccountsText = () => {
  parseError.value = ''
  parsedAccounts.value = []

  if (!accountsText.value.trim()) {
    return
  }

  const lines = accountsText.value.trim().split('\n')
  const accounts: Array<{ content: string; notes?: string; expiresAt?: string }> = []

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim()
    if (!line) continue

    const parts = line.split('|')
    const content = parts[0]?.trim()

    if (!content) {
      parseError.value = `第 ${i + 1} 行：账号内容不能为空`
      return
    }

    const account: { content: string; notes?: string; expiresAt?: string } = { content }

    // 解析备注
    if (parts[1]?.trim()) {
      account.notes = parts[1].trim()
    }

    // 解析过期时间
    if (parts[2]?.trim()) {
      const expiresAt = new Date(parts[2].trim())
      if (isNaN(expiresAt.getTime())) {
        parseError.value = `第 ${i + 1} 行：过期时间格式不正确`
        return
      }
      account.expiresAt = expiresAt.toISOString()
    }

    accounts.push(account)
  }

  parsedAccounts.value = accounts
}

const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  if (file) {
    processFile(file)
  }
}

const handleFileDrop = (event: DragEvent) => {
  event.preventDefault()
  const file = event.dataTransfer?.files[0]
  if (file) {
    processFile(file)
  }
}

const processFile = async (file: File) => {
  fileParseError.value = ''
  
  // 验证文件类型和大小
  if (!file.name.match(/\.(txt|csv)$/i)) {
    fileParseError.value = '只支持 .txt 和 .csv 文件'
    return
  }
  
  if (file.size > 10 * 1024 * 1024) {
    fileParseError.value = '文件大小不能超过 10MB'
    return
  }

  selectedFile.value = file

  try {
    const text = await file.text()
    accountsText.value = text
    parseAccountsText()
  } catch (error) {
    fileParseError.value = '文件读取失败'
  }
}

const clearFile = () => {
  selectedFile.value = null
  accountsText.value = ''
  parsedAccounts.value = []
  fileParseError.value = ''
}

const handleSubmit = async () => {
  if (parsedAccounts.value.length === 0) {
    return
  }

  submitting.value = true

  try {
    // 应用全局设置
    const accounts = parsedAccounts.value.map(account => ({
      ...account,
      notes: account.notes || globalSettings.value.notes || undefined,
      expiresAt: globalSettings.value.expiresAt || account.expiresAt || undefined
    }))

    const submitData: BatchImportForm = {
      poolId: props.poolId,
      accounts
    }

    emit('submit', submitData)
  } finally {
    submitting.value = false
  }
}

const handleClose = () => {
  emit('close')
  resetForm()
}

const resetForm = () => {
  importMode.value = 'text'
  accountsText.value = ''
  selectedFile.value = null
  parsedAccounts.value = []
  parseError.value = ''
  fileParseError.value = ''
  globalSettings.value = {
    notes: '',
    expiresAt: ''
  }
}

// 监听器
watch(() => props.show, (show) => {
  if (show) {
    resetForm()
  }
})
</script>
