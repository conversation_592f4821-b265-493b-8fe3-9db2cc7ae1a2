<template>
  <Teleport to="body">
    <Transition
      enter-active-class="transition-opacity duration-300"
      enter-from-class="opacity-0"
      enter-to-class="opacity-100"
      leave-active-class="transition-opacity duration-300"
      leave-from-class="opacity-100"
      leave-to-class="opacity-0"
    >
      <div
        v-if="show"
        class="fixed inset-0 z-50 overflow-y-auto"
        @click="handleBackdropClick"
      >
        <!-- Backdrop -->
        <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity"></div>
        
        <!-- Modal container -->
        <div class="flex min-h-full items-center justify-center p-4">
          <Transition
            enter-active-class="transition-all duration-300"
            enter-from-class="opacity-0 scale-95"
            enter-to-class="opacity-100 scale-100"
            leave-active-class="transition-all duration-300"
            leave-from-class="opacity-100 scale-100"
            leave-to-class="opacity-0 scale-95"
          >
            <div
              v-if="show"
              :class="modalClasses"
              @click.stop
            >
              <!-- Header -->
              <div v-if="title || $slots.header || !hideCloseButton" class="flex items-center justify-between p-6 border-b border-gray-200">
                <div class="flex-1">
                  <slot name="header">
                    <h3 v-if="title" class="text-lg font-semibold text-gray-900">{{ title }}</h3>
                  </slot>
                </div>
                
                <button
                  v-if="!hideCloseButton"
                  @click="handleClose"
                  class="ml-4 text-gray-400 hover:text-gray-600 transition-colors"
                  data-modal-close
                >
                  <span class="sr-only">关闭</span>
                  <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              
              <!-- Body -->
              <div :class="bodyClasses">
                <slot />
              </div>
              
              <!-- Footer -->
              <div v-if="$slots.footer" class="flex items-center justify-end gap-3 p-6 border-t border-gray-200">
                <slot name="footer" />
              </div>
            </div>
          </Transition>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
interface Props {
  show: boolean
  title?: string
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full'
  hideCloseButton?: boolean
  closeOnBackdrop?: boolean
  persistent?: boolean
}

interface Emits {
  'update:show': [value: boolean]
  close: []
}

const props = withDefaults(defineProps<Props>(), {
  size: 'md',
  hideCloseButton: false,
  closeOnBackdrop: true,
  persistent: false
})

const emit = defineEmits<Emits>()

const modalClasses = computed(() => {
  const baseClasses = [
    'relative bg-white rounded-lg shadow-xl',
    'w-full max-h-[90vh] overflow-hidden',
    'flex flex-col'
  ]

  const sizeClasses = {
    sm: 'max-w-md',
    md: 'max-w-lg',
    lg: 'max-w-2xl',
    xl: 'max-w-4xl',
    full: 'max-w-7xl mx-4'
  }

  return [
    ...baseClasses,
    sizeClasses[props.size]
  ].join(' ')
})

const bodyClasses = computed(() => {
  return [
    'flex-1 overflow-y-auto p-6',
    // Remove top padding if there's a header
    (props.title || props.$slots?.header) ? 'pt-0' : ''
  ].filter(Boolean).join(' ')
})

const handleClose = () => {
  if (!props.persistent) {
    emit('update:show', false)
    emit('close')
  }
}

const handleBackdropClick = () => {
  if (props.closeOnBackdrop && !props.persistent) {
    handleClose()
  }
}

// Handle escape key
const handleEscape = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && props.show && !props.persistent) {
    handleClose()
  }
}

// Prevent body scroll when modal is open
const preventBodyScroll = () => {
  if (props.show) {
    document.body.style.overflow = 'hidden'
  } else {
    document.body.style.overflow = ''
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleEscape)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleEscape)
  document.body.style.overflow = ''
})

watch(() => props.show, preventBodyScroll, { immediate: true })
</script>
