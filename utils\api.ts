/**
 * API调用工具函数
 * 封装常用的API调用方法，提供统一的错误处理和加载状态管理
 */

import type { 
  ApiResponse, 
  PoolType, 
  Pool, 
  Account, 
  ApiKey,
  CreatePoolTypeForm,
  CreatePoolForm,
  CreateAccountForm,
  BatchImportForm,
  UpdateAccountStatusForm,
  AccountFilters,
  PoolFilters,
  SystemStats,
  GetAccountParams,
  GetAccountResponse,
  ReportAccountParams,
  ReportAccountResponse,
  HealthCheckResponse
} from '~/types'

/**
 * 基础API调用函数
 */
async function apiCall<T>(
  url: string, 
  options: any = {},
  showLoading = true
): Promise<T> {
  const uiStore = useUiStore()
  const loadingKey = `api_${url.replace(/[^a-zA-Z0-9]/g, '_')}`

  try {
    if (showLoading) {
      uiStore.setLoadingState(loadingKey, true)
    }

    const response = await $fetch<ApiResponse<T>>(url, {
      ...options,
      onResponseError({ response }) {
        throw new Error(response._data?.message || response.statusText || 'API request failed')
      }
    })

    if (!response.success) {
      throw new Error(response.message || 'API request failed')
    }

    return response.data as T
  } catch (error: any) {
    // 显示错误通知
    uiStore.showError('请求失败', error.message)
    throw error
  } finally {
    if (showLoading) {
      uiStore.setLoadingState(loadingKey, false)
    }
  }
}

// ==================== API密钥管理 ====================

export const apiKeyApi = {
  /**
   * 获取API密钥
   */
  async get(): Promise<ApiKey> {
    return apiCall<ApiKey>('/api/admin/apikey')
  },

  /**
   * 重新生成API密钥
   */
  async regenerate(): Promise<ApiKey> {
    return apiCall<ApiKey>('/api/admin/apikey', { method: 'POST' })
  },

  /**
   * 获取所有API密钥
   */
  async getAll(): Promise<ApiKey[]> {
    return apiCall<ApiKey[]>('/api/admin/api-keys')
  },

  /**
   * 创建API密钥
   */
  async create(data: { name: string; description?: string; expiresAt?: string }): Promise<ApiKey> {
    return apiCall<ApiKey>('/api/admin/api-keys', {
      method: 'POST',
      body: data
    })
  },

  /**
   * 更新API密钥
   */
  async update(id: number, data: { name: string; description?: string; expiresAt?: string }): Promise<ApiKey> {
    return apiCall<ApiKey>(`/api/admin/api-keys/${id}`, {
      method: 'PUT',
      body: data
    })
  },

  /**
   * 删除API密钥
   */
  async delete(id: number): Promise<void> {
    return apiCall<void>(`/api/admin/api-keys/${id}`, {
      method: 'DELETE'
    })
  },

  /**
   * 更新API密钥状态
   */
  async updateStatus(id: number, status: string): Promise<ApiKey> {
    return apiCall<ApiKey>(`/api/admin/api-keys/${id}/status`, {
      method: 'PUT',
      body: { status }
    })
  },

  /**
   * 批量删除API密钥
   */
  async batchDelete(apiKeyIds: number[]): Promise<{ deletedCount: number; deletedIds: number[] }> {
    return apiCall<{ deletedCount: number; deletedIds: number[] }>('/api/admin/api-keys/batch-delete', {
      method: 'POST',
      body: { apiKeyIds }
    })
  },

  /**
   * 批量修改API密钥状态
   */
  async batchUpdateStatus(apiKeyIds: number[], status: string): Promise<{ updatedCount: number; updatedIds: number[]; newStatus: string }> {
    return apiCall<{ updatedCount: number; updatedIds: number[]; newStatus: string }>('/api/admin/api-keys/batch-status', {
      method: 'PUT',
      body: { apiKeyIds, status }
    })
  }
}

// ==================== 号池类型管理 ====================

export const poolTypeApi = {
  /**
   * 获取所有号池类型
   */
  async getAll(): Promise<PoolType[]> {
    return apiCall<PoolType[]>('/api/admin/pool-types')
  },

  /**
   * 获取单个号池类型
   */
  async getById(id: string): Promise<PoolType> {
    return apiCall<PoolType>(`/api/admin/pool-types/${id}`)
  },

  /**
   * 创建号池类型
   */
  async create(data: CreatePoolTypeForm): Promise<PoolType> {
    return apiCall<PoolType>('/api/admin/pool-types', {
      method: 'POST',
      body: data
    })
  },

  /**
   * 更新号池类型
   */
  async update(id: string, data: CreatePoolTypeForm): Promise<PoolType> {
    return apiCall<PoolType>(`/api/admin/pool-types/${id}`, {
      method: 'PUT',
      body: data
    })
  },

  /**
   * 删除号池类型
   */
  async delete(id: string): Promise<void> {
    return apiCall<void>(`/api/admin/pool-types/${id}`, {
      method: 'DELETE'
    })
  }
}

// ==================== 号池管理 ====================

export const poolApi = {
  /**
   * 获取所有号池
   */
  async getAll(filters?: PoolFilters): Promise<Pool[]> {
    const query = new URLSearchParams()
    if (filters?.poolTypeId) query.append('poolTypeId', filters.poolTypeId)
    if (filters?.groupByType) query.append('groupByType', 'true')
    
    const url = `/api/admin/pools${query.toString() ? '?' + query.toString() : ''}`
    return apiCall<Pool[]>(url)
  },

  /**
   * 获取单个号池
   */
  async getById(id: string): Promise<Pool> {
    return apiCall<Pool>(`/api/admin/pools/${id}`)
  },

  /**
   * 创建号池
   */
  async create(data: CreatePoolForm): Promise<Pool> {
    return apiCall<Pool>('/api/admin/pools', {
      method: 'POST',
      body: data
    })
  },

  /**
   * 更新号池
   */
  async update(id: string, data: Partial<CreatePoolForm>): Promise<Pool> {
    return apiCall<Pool>(`/api/admin/pools/${id}`, {
      method: 'PUT',
      body: data
    })
  },

  /**
   * 删除号池
   */
  async delete(id: string): Promise<void> {
    return apiCall<void>(`/api/admin/pools/${id}`, {
      method: 'DELETE'
    })
  }
}

// ==================== 账号管理 ====================

export const accountApi = {
  /**
   * 获取号池下的账号列表
   */
  async getByPool(poolId: string, filters?: AccountFilters & { page?: number; limit?: number }): Promise<{
    accounts: Account[]
    pagination: any
    pool: Pool
    filters: any
  }> {
    const query = new URLSearchParams()
    if (filters?.status) query.append('status', filters.status)
    if (filters?.search) query.append('search', filters.search)
    if (filters?.page) query.append('page', filters.page.toString())
    if (filters?.limit) query.append('limit', filters.limit.toString())
    if (filters?.sortBy) query.append('sortBy', filters.sortBy)
    if (filters?.sortOrder) query.append('sortOrder', filters.sortOrder)
    
    const url = `/api/admin/pools/${poolId}/accounts${query.toString() ? '?' + query.toString() : ''}`
    return apiCall<any>(url)
  },

  /**
   * 创建单个账号
   */
  async create(data: CreateAccountForm): Promise<Account> {
    return apiCall<Account>('/api/admin/accounts', {
      method: 'POST',
      body: data
    })
  },

  /**
   * 批量导入账号
   */
  async batchImport(data: BatchImportForm): Promise<{ importedCount: number }> {
    return apiCall<{ importedCount: number }>('/api/admin/accounts/batch', {
      method: 'POST',
      body: data
    })
  },

  /**
   * 更新账号状态
   */
  async updateStatus(id: string, data: UpdateAccountStatusForm): Promise<Account> {
    return apiCall<Account>(`/api/admin/accounts/${id}/status`, {
      method: 'PUT',
      body: data
    })
  },

  /**
   * 强制释放账号
   */
  async release(id: string): Promise<Account> {
    return apiCall<Account>(`/api/admin/accounts/${id}/release`, {
      method: 'POST'
    })
  },

  /**
   * 批量删除账号
   */
  async batchDelete(accountIds: string[]): Promise<{ deletedCount: number; deletedIds: string[] }> {
    return apiCall<{ deletedCount: number; deletedIds: string[] }>('/api/admin/accounts/batch-delete', {
      method: 'POST',
      body: { accountIds }
    })
  },

  /**
   * 批量修改账号状态
   */
  async batchUpdateStatus(accountIds: string[], status: string): Promise<{ updatedCount: number; updatedIds: string[]; newStatus: string }> {
    return apiCall<{ updatedCount: number; updatedIds: string[]; newStatus: string }>('/api/admin/accounts/batch-status', {
      method: 'PUT',
      body: { accountIds, status }
    })
  },

  /**
   * 删除账号
   */
  async delete(id: string): Promise<void> {
    return apiCall<void>(`/api/admin/accounts/${id}`, {
      method: 'DELETE'
    })
  },

  /**
   * 清理过期账号
   */
  async cleanupExpired(): Promise<{ updatedCount: number }> {
    return apiCall<{ updatedCount: number }>('/api/admin/accounts/cleanup-expired', {
      method: 'POST'
    })
  }
}

// ==================== 核心业务API ====================

export const coreApi = {
  /**
   * 获取账号（取号）
   */
  async getAccount(params: GetAccountParams, apiKey: string): Promise<GetAccountResponse> {
    const query = new URLSearchParams()
    if (params.poolName) query.append('poolName', params.poolName)
    if (params.poolType) query.append('poolType', params.poolType)
    
    const url = `/api/v1/account${query.toString() ? '?' + query.toString() : ''}`
    return apiCall<GetAccountResponse>(url, {
      headers: {
        'X-API-KEY': apiKey
      }
    }, false) // 不显示加载状态，因为这是核心业务API
  },

  /**
   * 上报账号状态（归还）
   */
  async reportAccount(data: ReportAccountParams, apiKey: string): Promise<ReportAccountResponse> {
    return apiCall<ReportAccountResponse>('/api/v1/account/report', {
      method: 'POST',
      headers: {
        'X-API-KEY': apiKey
      },
      body: data
    }, false)
  },

  /**
   * 获取统计信息
   */
  async getStats(apiKey: string, filters?: { poolName?: string; poolType?: string }): Promise<SystemStats> {
    const query = new URLSearchParams()
    if (filters?.poolName) query.append('poolName', filters.poolName)
    if (filters?.poolType) query.append('poolType', filters.poolType)
    
    const url = `/api/v1/stats${query.toString() ? '?' + query.toString() : ''}`
    return apiCall<SystemStats>(url, {
      headers: {
        'X-API-KEY': apiKey
      }
    }, false)
  },

  /**
   * 健康检查
   */
  async healthCheck(): Promise<HealthCheckResponse> {
    return apiCall<HealthCheckResponse>('/api/v1/health', {}, false)
  }
}
