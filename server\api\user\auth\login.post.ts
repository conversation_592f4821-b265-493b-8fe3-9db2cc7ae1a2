import { z } from 'zod'
import { prisma } from '~/server/utils/db'
import { verifyPassword } from '~/server/utils/auth'

// 验证登录请求的schema
const loginSchema = z.object({
  username: z.string().min(1, '用户名或邮箱不能为空'),
  password: z.string().min(1, '密码不能为空')
})

export default defineEventHandler(async (event) => {
  try {
    // 验证请求体
    const body = await readBody(event)
    const { username, password } = loginSchema.parse(body)

    // 判断输入的是邮箱还是用户名
    const isEmail = username.includes('@')

    // 查找用户
    const user = await prisma.user.findFirst({
      where: isEmail
        ? { email: username }
        : { name: username }
    })

    if (!user) {
      throw createError({
        statusCode: 401,
        statusMessage: '用户名或密码错误'
      })
    }

    // 检查用户是否设置了密码
    if (!user.passwordHash) {
      throw createError({
        statusCode: 401,
        statusMessage: '该用户未设置密码，请联系管理员'
      })
    }

    // 检查用户状态
    if (user.status !== 'Active') {
      throw createError({
        statusCode: 401,
        statusMessage: '账号已被禁用，请联系管理员'
      })
    }

    // 验证密码
    const isValidPassword = await verifyPassword(password, user.passwordHash)
    if (!isValidPassword) {
      throw createError({
        statusCode: 401,
        statusMessage: '用户名或密码错误'
      })
    }

    // 更新最后登录时间
    await prisma.user.update({
      where: { id: user.id },
      data: { lastLoginAt: new Date() }
    })

    // 创建用户会话
    const sessionToken = generateRandomString(64)
    const expiresAt = new Date()
    expiresAt.setDate(expiresAt.getDate() + 7) // 7天过期

    const session = await prisma.userSession.create({
      data: {
        userId: user.id,
        token: sessionToken,
        expiresAt
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            status: true,
            createdAt: true
          }
        }
      }
    })

    // 设置Cookie
    setCookie(event, 'user_token', sessionToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 60 * 60 * 24 * 7, // 7天
      path: '/'
    })

    return {
      success: true,
      message: '登录成功',
      user: session.user
    }

  } catch (error) {
    console.error('User login error:', error)
    
    if (error.name === 'ZodError') {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid request data',
        data: { message: error.errors[0]?.message || '请求数据无效' }
      })
    }

    if (error.statusCode) {
      throw error
    }

    throw createError({
      statusCode: 500,
      statusMessage: 'Login failed'
    })
  }
})

// 生成随机字符串的辅助函数
function generateRandomString(length: number): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}
