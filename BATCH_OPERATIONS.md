# 批量操作功能说明

本文档说明新增的批量操作功能的使用方法和技术实现。

## 🆕 新增功能

### 1. 号池账号列表批量操作
- **位置**：号池详情页面 (`/pools/[id]`)
- **功能**：
  - 复选框选择多个账号
  - 批量删除选中的账号
  - 批量修改选中账号的状态

### 2. API密钥管理批量操作
- **位置**：API密钥管理页面 (`/api-keys`)
- **功能**：
  - 复选框选择多个API密钥
  - 批量删除选中的API密钥
  - 批量修改选中API密钥的状态

## 🎯 使用方法

### 号池账号批量操作

1. **进入号池详情页面**
   - 访问 `/pools/[号池ID]`
   - 查看账号列表（现在以表格形式显示）

2. **选择账号**
   - 点击表格第一列的复选框选择单个账号
   - 可以选择多个账号

3. **批量操作**
   - 选择账号后，页面顶部会显示批量操作按钮
   - **批量修改状态**：选择新状态，点击"批量修改状态"按钮
   - **批量删除**：点击"批量删除"按钮

### API密钥批量操作

1. **进入API密钥管理页面**
   - 访问 `/api-keys`
   - 查看API密钥列表（现在以表格形式显示）

2. **选择API密钥**
   - 点击表格第一列的复选框选择单个API密钥
   - 可以选择多个API密钥

3. **批量操作**
   - 选择API密钥后，页面顶部会显示批量操作按钮
   - **批量修改状态**：选择"启用"或"禁用"，点击"批量修改状态"按钮
   - **批量删除**：点击"批量删除"按钮

## 🔒 安全特性

### 确认对话框
- 所有批量操作都会弹出确认对话框
- 显示将要操作的项目数量
- 防止误操作

### 状态验证
- 批量修改状态时会验证状态值的有效性
- 过期的API密钥无法被激活
- 正在使用的账号无法被删除

## 🛠 技术实现

### 后端API接口

#### 账号批量操作
```
POST /api/admin/accounts/batch-delete
PUT /api/admin/accounts/batch-status
```

#### API密钥批量操作
```
POST /api/admin/api-keys/batch-delete
PUT /api/admin/api-keys/batch-status
```

### 前端组件

#### AccountTable.vue
- 表格形式显示账号列表
- 支持复选框选择
- 集成批量操作功能
- 保留原有的单个操作功能

#### ApiKeyTable.vue
- 表格形式显示API密钥列表
- 支持复选框选择
- 集成批量操作功能
- 保留原有的单个操作功能

### 数据流程

1. **用户选择**：前端收集选中的项目ID
2. **批量操作**：调用相应的批量API接口
3. **服务器处理**：验证数据并执行批量操作
4. **结果反馈**：返回操作结果并更新前端显示

## 📊 API接口详情

### 批量删除账号
```http
POST /api/admin/accounts/batch-delete
Content-Type: application/json

{
  "accountIds": ["id1", "id2", "id3"]
}
```

### 批量修改账号状态
```http
PUT /api/admin/accounts/batch-status
Content-Type: application/json

{
  "accountIds": ["id1", "id2", "id3"],
  "status": "Available"
}
```

### 批量删除API密钥
```http
POST /api/admin/api-keys/batch-delete
Content-Type: application/json

{
  "apiKeyIds": [1, 2, 3]
}
```

### 批量修改API密钥状态
```http
PUT /api/admin/api-keys/batch-status
Content-Type: application/json

{
  "apiKeyIds": [1, 2, 3],
  "status": "Active"
}
```

## 🎨 界面变化

### 从卡片列表到表格
- **之前**：账号和API密钥以卡片形式显示
- **现在**：以表格形式显示，更加紧凑和易于管理

### 新增的UI元素
- 复选框列（表格第一列）
- 批量操作按钮区域（选择项目后显示）
- 状态选择下拉框（批量修改状态时使用）

## 🔍 注意事项

1. **权限控制**：确保用户有相应的操作权限
2. **数据备份**：批量删除操作不可逆，建议操作前备份重要数据
3. **性能考虑**：大量数据的批量操作可能需要时间，请耐心等待
4. **错误处理**：如果批量操作失败，会显示相应的错误信息

## 🚀 未来扩展

可以考虑的功能扩展：
- 全选/反选功能
- 批量导出功能
- 批量编辑功能
- 操作历史记录
- 撤销操作功能
