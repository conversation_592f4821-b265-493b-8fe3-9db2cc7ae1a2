<template>
  <div class="min-h-screen bg-gradient-to-br from-green-50 to-blue-100 flex items-center justify-center p-4">
    <div class="max-w-md w-full">
      <!-- 品牌标识 -->
      <div class="text-center mb-8">
        <div class="mx-auto w-16 h-16 bg-gradient-to-r from-green-600 to-blue-600 rounded-2xl flex items-center justify-center mb-4 shadow-lg">
          <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/>
          </svg>
        </div>
        <h1 class="text-3xl font-bold text-gray-900 mb-2">EasyPool</h1>
        <p class="text-gray-600">用户登录</p>
      </div>

      <!-- 登录卡片 -->
      <div class="bg-white rounded-2xl shadow-xl p-8 backdrop-blur-sm bg-opacity-95">
        <div class="text-center mb-8">
          <h2 class="text-2xl font-bold text-gray-900 mb-2">欢迎回来</h2>
          <p class="text-gray-600">请登录您的用户账号</p>
        </div>

        <form class="space-y-6" @submit.prevent="handleLogin">
          <!-- 用户名或邮箱输入 -->
          <div>
            <label for="username" class="block text-sm font-medium text-gray-700 mb-2">
              用户名或邮箱
            </label>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
              </div>
              <input
                id="username"
                v-model="form.username"
                name="username"
                type="text"
                required
                class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-200 text-gray-900 placeholder-gray-500"
                placeholder="请输入用户名或邮箱"
              />
            </div>
          </div>
          
          <!-- 密码输入 -->
          <div>
            <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
              密码
            </label>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
              </div>
              <input
                id="password"
                v-model="form.password"
                name="password"
                type="password"
                required
                class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-200 text-gray-900 placeholder-gray-500"
                placeholder="请输入密码"
              />
            </div>
          </div>

          <!-- 错误提示 -->
          <div v-if="error" class="rounded-xl bg-red-50 border border-red-200 p-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">登录失败</h3>
                <div class="mt-1 text-sm text-red-700">{{ error }}</div>
              </div>
            </div>
          </div>

          <!-- 登录按钮 -->
          <button
            type="submit"
            :disabled="loading || !form.username.trim() || !form.password.trim()"
            class="w-full flex justify-center items-center py-3 px-4 border border-transparent rounded-xl text-sm font-medium text-white bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl"
          >
            <svg
              v-if="loading"
              class="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            <svg
              v-else
              class="-ml-1 mr-3 h-5 w-5 text-white"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
            </svg>
            {{ loading ? '登录中...' : '立即登录' }}
          </button>

          <!-- 底部链接 -->
          <div class="text-center pt-4 space-y-2">
            <p class="text-sm text-gray-600">
              <NuxtLink to="/admin/login" class="text-green-600 hover:text-green-500">
                管理员登录
              </NuxtLink>
            </p>
            <p class="text-xs text-gray-500">
              © 2024 EasyPool. 保留所有权利。
            </p>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup>
// 页面元数据
definePageMeta({
  title: '用户登录 - EasyPool',
  layout: false
})

// 设置页面标题
useHead({
  title: '用户登录 - EasyPool'
})

// 响应式数据
const form = ref({
  username: '',
  password: ''
})

const loading = ref(false)
const error = ref('')

// 登录处理
const handleLogin = async () => {
  if (loading.value) return
  
  try {
    loading.value = true
    error.value = ''

    const response = await $fetch('/api/user/auth/login', {
      method: 'POST',
      body: {
        username: form.value.username.trim(),
        password: form.value.password
      }
    })

    if (response.success) {
      // 登录成功，重定向到用户仪表板
      await navigateTo('/user/dashboard')
    } else {
      error.value = response.message || '登录失败'
    }
  } catch (err) {
    console.error('Login error:', err)
    error.value = err.data?.message || '登录失败，请检查邮箱和密码'
  } finally {
    loading.value = false
  }
}

// 页面加载时检查是否已登录
onMounted(async () => {
  try {
    const response = await $fetch('/api/user/auth/me')
    if (response.success) {
      // 已登录，重定向到仪表板
      await navigateTo('/user/dashboard')
    }
  } catch (error) {
    // 未登录，继续显示登录页面
  }
})
</script>
