/**
 * 数据格式化工具
 * 提供各种数据格式化功能，包括日期、数字、文本等
 */

import type { DateFormat, FormatOptions, AccountStatus } from '~/types'

/**
 * 日期格式化
 */
export function formatDate(
  date: string | Date | null | undefined,
  format: DateFormat = 'datetime',
  options: FormatOptions = {}
): string {
  if (!date) return '-'

  const dateObj = typeof date === 'string' ? new Date(date) : date
  
  if (isNaN(dateObj.getTime())) return '-'

  const { locale = 'zh-CN', timezone = 'Asia/Shanghai' } = options

  try {
    switch (format) {
      case 'date':
        return dateObj.toLocaleDateString(locale, { 
          timeZone: timezone,
          year: 'numeric',
          month: '2-digit',
          day: '2-digit'
        })
      
      case 'time':
        return dateObj.toLocaleTimeString(locale, { 
          timeZone: timezone,
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit'
        })
      
      case 'datetime':
        return dateObj.toLocaleString(locale, { 
          timeZone: timezone,
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit'
        })
      
      case 'relative':
        return formatRelativeTime(dateObj)
      
      default:
        return dateObj.toLocaleString(locale, { timeZone: timezone })
    }
  } catch (error) {
    console.warn('Date formatting error:', error)
    return dateObj.toString()
  }
}

/**
 * 相对时间格式化
 */
export function formatRelativeTime(date: Date): string {
  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffSeconds = Math.floor(diffMs / 1000)
  const diffMinutes = Math.floor(diffSeconds / 60)
  const diffHours = Math.floor(diffMinutes / 60)
  const diffDays = Math.floor(diffHours / 24)

  if (diffSeconds < 60) {
    return '刚刚'
  } else if (diffMinutes < 60) {
    return `${diffMinutes}分钟前`
  } else if (diffHours < 24) {
    return `${diffHours}小时前`
  } else if (diffDays < 7) {
    return `${diffDays}天前`
  } else {
    return formatDate(date, 'date')
  }
}

/**
 * 数字格式化
 */
export function formatNumber(
  value: number | null | undefined,
  options: {
    decimals?: number
    separator?: string
    prefix?: string
    suffix?: string
  } = {}
): string {
  if (value === null || value === undefined || isNaN(value)) return '-'

  const { decimals = 0, separator = ',', prefix = '', suffix = '' } = options

  const formatted = value.toFixed(decimals).replace(/\B(?=(\d{3})+(?!\d))/g, separator)
  return `${prefix}${formatted}${suffix}`
}

/**
 * 百分比格式化
 */
export function formatPercentage(
  value: number | null | undefined,
  decimals = 1
): string {
  if (value === null || value === undefined || isNaN(value)) return '-'
  return `${value.toFixed(decimals)}%`
}

/**
 * 文件大小格式化
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B'

  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`
}

/**
 * 账号状态格式化
 */
export function formatAccountStatus(status: AccountStatus): {
  text: string
  color: string
  bgColor: string
} {
  const statusMap = {
    Available: {
      text: '可用',
      color: 'text-green-700',
      bgColor: 'bg-green-100'
    },
    InUse: {
      text: '占用中',
      color: 'text-blue-700',
      bgColor: 'bg-blue-100'
    },
    Invalid: {
      text: '失效',
      color: 'text-red-700',
      bgColor: 'bg-red-100'
    },
    Failed: {
      text: '失败',
      color: 'text-orange-700',
      bgColor: 'bg-orange-100'
    },
    Expired: {
      text: '已过期',
      color: 'text-gray-700',
      bgColor: 'bg-gray-100'
    }
  }

  return statusMap[status] || {
    text: status,
    color: 'text-gray-700',
    bgColor: 'bg-gray-100'
  }
}

/**
 * 文本截断
 */
export function truncateText(
  text: string | null | undefined,
  maxLength = 50,
  suffix = '...'
): string {
  if (!text) return '-'
  
  if (text.length <= maxLength) return text
  
  return text.substring(0, maxLength - suffix.length) + suffix
}

/**
 * 脱敏处理
 */
export function maskText(
  text: string | null | undefined,
  visibleStart = 4,
  visibleEnd = 4,
  maskChar = '*'
): string {
  if (!text) return '-'
  
  if (text.length <= visibleStart + visibleEnd) {
    return text
  }
  
  const start = text.substring(0, visibleStart)
  const end = text.substring(text.length - visibleEnd)
  const maskLength = Math.max(3, text.length - visibleStart - visibleEnd)
  const mask = maskChar.repeat(maskLength)
  
  return `${start}${mask}${end}`
}

/**
 * 高亮搜索关键词
 */
export function highlightText(
  text: string,
  searchTerm: string,
  className = 'bg-yellow-200'
): string {
  if (!searchTerm || !text) return text

  const regex = new RegExp(`(${escapeRegExp(searchTerm)})`, 'gi')
  return text.replace(regex, `<span class="${className}">$1</span>`)
}

/**
 * 转义正则表达式特殊字符
 */
function escapeRegExp(string: string): string {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
}

/**
 * 生成随机颜色
 */
export function generateColor(seed: string): string {
  let hash = 0
  for (let i = 0; i < seed.length; i++) {
    hash = seed.charCodeAt(i) + ((hash << 5) - hash)
  }
  
  const hue = hash % 360
  return `hsl(${hue}, 70%, 50%)`
}

/**
 * 格式化持续时间
 */
export function formatDuration(ms: number): string {
  const seconds = Math.floor(ms / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  const days = Math.floor(hours / 24)

  if (days > 0) {
    return `${days}天${hours % 24}小时`
  } else if (hours > 0) {
    return `${hours}小时${minutes % 60}分钟`
  } else if (minutes > 0) {
    return `${minutes}分钟${seconds % 60}秒`
  } else {
    return `${seconds}秒`
  }
}

/**
 * 验证和格式化JSON
 */
export function formatJSON(jsonString: string): { valid: boolean; formatted?: string; error?: string } {
  try {
    const parsed = JSON.parse(jsonString)
    return {
      valid: true,
      formatted: JSON.stringify(parsed, null, 2)
    }
  } catch (error: any) {
    return {
      valid: false,
      error: error.message
    }
  }
}
