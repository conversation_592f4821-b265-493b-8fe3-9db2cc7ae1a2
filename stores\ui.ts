/**
 * UI状态管理
 * 管理全局UI状态，包括加载状态、错误提示、通知等
 */

import { defineStore } from 'pinia'

export type NotificationType = 'success' | 'error' | 'warning' | 'info'

export interface Notification {
  id: string
  type: NotificationType
  title: string
  message?: string
  duration?: number
  persistent?: boolean
  createdAt: string
}

export interface LoadingState {
  [key: string]: boolean
}

export interface UiState {
  isLoading: boolean
  loadingStates: LoadingState
  notifications: Notification[]
  sidebarOpen: boolean
  theme: 'light' | 'dark'
  pageTitle: string
}

export const useUiStore = defineStore('ui', {
  state: (): UiState => ({
    isLoading: false,
    loadingStates: {},
    notifications: [],
    sidebarOpen: false,
    theme: 'light',
    pageTitle: '简易号池管理系统'
  }),

  getters: {
    hasNotifications: (state) => state.notifications.length > 0,
    unreadNotifications: (state) => state.notifications.filter(n => !n.persistent),
    isAnyLoading: (state) => state.isLoading || Object.values(state.loadingStates).some(Bo<PERSON>an),
    getLoadingState: (state) => (key: string) => state.loadingStates[key] || false
  },

  actions: {
    /**
     * 设置全局加载状态
     */
    setLoading(loading: boolean) {
      this.isLoading = loading
    },

    /**
     * 设置特定操作的加载状态
     */
    setLoadingState(key: string, loading: boolean) {
      if (loading) {
        this.loadingStates[key] = true
      } else {
        delete this.loadingStates[key]
      }
    },

    /**
     * 添加通知
     */
    addNotification(notification: Omit<Notification, 'id' | 'createdAt'>) {
      const id = `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      const newNotification: Notification = {
        id,
        createdAt: new Date().toISOString(),
        duration: 5000, // 默认5秒
        ...notification
      }

      this.notifications.push(newNotification)

      // 自动移除非持久化通知
      if (!newNotification.persistent && newNotification.duration && newNotification.duration > 0) {
        setTimeout(() => {
          this.removeNotification(id)
        }, newNotification.duration)
      }

      return id
    },

    /**
     * 移除通知
     */
    removeNotification(id: string) {
      const index = this.notifications.findIndex(n => n.id === id)
      if (index > -1) {
        this.notifications.splice(index, 1)
      }
    },

    /**
     * 清除所有通知
     */
    clearNotifications() {
      this.notifications = []
    },

    /**
     * 显示成功通知
     */
    showSuccess(title: string, message?: string, duration?: number) {
      return this.addNotification({
        type: 'success',
        title,
        message,
        duration
      })
    },

    /**
     * 显示错误通知
     */
    showError(title: string, message?: string, persistent = false) {
      return this.addNotification({
        type: 'error',
        title,
        message,
        persistent,
        duration: persistent ? 0 : 8000 // 错误通知显示更长时间
      })
    },

    /**
     * 显示警告通知
     */
    showWarning(title: string, message?: string, duration?: number) {
      return this.addNotification({
        type: 'warning',
        title,
        message,
        duration
      })
    },

    /**
     * 显示信息通知
     */
    showInfo(title: string, message?: string, duration?: number) {
      return this.addNotification({
        type: 'info',
        title,
        message,
        duration
      })
    },

    /**
     * 切换侧边栏状态
     */
    toggleSidebar() {
      this.sidebarOpen = !this.sidebarOpen
    },

    /**
     * 设置侧边栏状态
     */
    setSidebarOpen(open: boolean) {
      this.sidebarOpen = open
    },

    /**
     * 切换主题
     */
    toggleTheme() {
      this.theme = this.theme === 'light' ? 'dark' : 'light'
    },

    /**
     * 设置主题
     */
    setTheme(theme: 'light' | 'dark') {
      this.theme = theme
    },

    /**
     * 设置页面标题
     */
    setPageTitle(title: string) {
      this.pageTitle = title
      // 同时更新浏览器标题
      if (process.client) {
        document.title = `${title} - EasyPool`
      }
    }
  },

  // TODO: 添加持久化配置
  // persist: {
  //   storage: persistedState.localStorage,
  //   paths: ['theme', 'sidebarOpen']
  // }
})
