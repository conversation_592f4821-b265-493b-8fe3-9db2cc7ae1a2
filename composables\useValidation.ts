/**
 * 数据验证组合式函数
 * 提供常用的表单验证功能
 */

export interface ValidationRule {
  required?: boolean
  minLength?: number
  maxLength?: number
  pattern?: RegExp
  custom?: (value: any) => boolean | string
  message?: string
}

export interface ValidationResult {
  valid: boolean
  errors: string[]
}

export function useValidation() {
  /**
   * 验证单个字段
   */
  const validateField = (value: any, rules: ValidationRule[]): ValidationResult => {
    const errors: string[] = []

    for (const rule of rules) {
      // 必填验证
      if (rule.required && (value === null || value === undefined || value === '')) {
        errors.push(rule.message || '此字段为必填项')
        continue
      }

      // 如果值为空且不是必填，跳过其他验证
      if (!rule.required && (value === null || value === undefined || value === '')) {
        continue
      }

      // 最小长度验证
      if (rule.minLength && value.length < rule.minLength) {
        errors.push(rule.message || `最少需要${rule.minLength}个字符`)
      }

      // 最大长度验证
      if (rule.maxLength && value.length > rule.maxLength) {
        errors.push(rule.message || `最多允许${rule.maxLength}个字符`)
      }

      // 正则表达式验证
      if (rule.pattern && !rule.pattern.test(value)) {
        errors.push(rule.message || '格式不正确')
      }

      // 自定义验证
      if (rule.custom) {
        const result = rule.custom(value)
        if (result !== true) {
          errors.push(typeof result === 'string' ? result : (rule.message || '验证失败'))
        }
      }
    }

    return {
      valid: errors.length === 0,
      errors
    }
  }

  /**
   * 验证表单对象
   */
  const validateForm = (
    data: Record<string, any>,
    rules: Record<string, ValidationRule[]>
  ): { valid: boolean; errors: Record<string, string[]> } => {
    const errors: Record<string, string[]> = {}
    let valid = true

    for (const [field, fieldRules] of Object.entries(rules)) {
      const result = validateField(data[field], fieldRules)
      if (!result.valid) {
        errors[field] = result.errors
        valid = false
      }
    }

    return { valid, errors }
  }

  // 常用验证规则
  const rules = {
    /**
     * 必填规则
     */
    required: (message?: string): ValidationRule => ({
      required: true,
      message: message || '此字段为必填项'
    }),

    /**
     * 长度范围规则
     */
    length: (min?: number, max?: number, message?: string): ValidationRule => ({
      minLength: min,
      maxLength: max,
      message: message || (min && max ? `长度应在${min}-${max}个字符之间` : 
                          min ? `最少需要${min}个字符` : 
                          max ? `最多允许${max}个字符` : undefined)
    }),

    /**
     * 邮箱格式规则
     */
    email: (message?: string): ValidationRule => ({
      pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
      message: message || '请输入有效的邮箱地址'
    }),

    /**
     * URL格式规则
     */
    url: (message?: string): ValidationRule => ({
      pattern: /^https?:\/\/.+/,
      message: message || '请输入有效的URL地址'
    }),

    /**
     * 数字格式规则
     */
    number: (message?: string): ValidationRule => ({
      pattern: /^\d+$/,
      message: message || '请输入有效的数字'
    }),

    /**
     * 手机号格式规则
     */
    phone: (message?: string): ValidationRule => ({
      pattern: /^1[3-9]\d{9}$/,
      message: message || '请输入有效的手机号码'
    }),

    /**
     * 日期格式规则
     */
    date: (message?: string): ValidationRule => ({
      custom: (value: string) => {
        if (!value) return true
        const date = new Date(value)
        return !isNaN(date.getTime())
      },
      message: message || '请输入有效的日期'
    }),

    /**
     * 未来日期规则
     */
    futureDate: (message?: string): ValidationRule => ({
      custom: (value: string) => {
        if (!value) return true
        const date = new Date(value)
        return date > new Date()
      },
      message: message || '日期必须是未来时间'
    }),

    /**
     * JSON格式规则
     */
    json: (message?: string): ValidationRule => ({
      custom: (value: string) => {
        if (!value) return true
        try {
          JSON.parse(value)
          return true
        } catch {
          return false
        }
      },
      message: message || '请输入有效的JSON格式'
    }),

    /**
     * 自定义规则
     */
    custom: (validator: (value: any) => boolean | string, message?: string): ValidationRule => ({
      custom: validator,
      message
    })
  }

  /**
   * 创建响应式验证状态
   */
  const createValidationState = <T extends Record<string, any>>(
    initialData: T,
    validationRules: Record<keyof T, ValidationRule[]>
  ) => {
    const data = ref<T>({ ...initialData })
    const errors = ref<Record<string, string[]>>({})
    const touched = ref<Record<string, boolean>>({})

    const validate = () => {
      const result = validateForm(data.value, validationRules)
      errors.value = result.errors
      return result.valid
    }

    const validateField = (field: keyof T) => {
      const result = validateField(data.value[field], validationRules[field] || [])
      if (result.valid) {
        delete errors.value[field as string]
      } else {
        errors.value[field as string] = result.errors
      }
      return result.valid
    }

    const touch = (field: keyof T) => {
      touched.value[field as string] = true
    }

    const reset = () => {
      data.value = { ...initialData }
      errors.value = {}
      touched.value = {}
    }

    const isValid = computed(() => Object.keys(errors.value).length === 0)
    const hasErrors = computed(() => Object.keys(errors.value).length > 0)

    return {
      data,
      errors,
      touched,
      isValid,
      hasErrors,
      validate,
      validateField,
      touch,
      reset
    }
  }

  return {
    validateField,
    validateForm,
    rules,
    createValidationState
  }
}
