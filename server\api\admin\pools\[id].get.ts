/**
 * 获取单个号池详情接口
 * GET /api/admin/pools/[id]
 */

import { prisma } from '../../../utils/db'

export default defineEventHandler(async (event) => {
  try {
    // 获取路由参数中的ID
    const id = getRouterParam(event, 'id')
    
    if (!id) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Pool ID is required'
      })
    }

    // 验证ID格式
    if (typeof id !== 'string' || id.length < 10) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid pool ID format'
      })
    }

    // 获取号池详情
    const pool = await prisma.pool.findUnique({
      where: { id },
      include: {
        poolType: true,
        _count: {
          select: {
            accounts: true
          }
        }
      }
    })

    if (!pool) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Pool not found'
      })
    }

    // 计算详细统计信息
    const accountStats = await prisma.account.groupBy({
      by: ['status'],
      where: {
        poolId: pool.id
      },
      _count: {
        status: true
      }
    })

    const stats = {
      total: 0,
      available: 0,
      inUse: 0,
      invalid: 0,
      expired: 0
    }

    accountStats.forEach(stat => {
      stats.total += stat._count.status
      switch (stat.status) {
        case 'Available':
          stats.available = stat._count.status
          break
        case 'InUse':
          stats.inUse = stat._count.status
          break
        case 'Invalid':
          stats.invalid = stat._count.status
          break
        case 'Expired':
          stats.expired = stat._count.status
          break
      }
    })

    // 获取最近的账号活动信息
    const recentAccounts = await prisma.account.findMany({
      where: {
        poolId: pool.id
      },
      orderBy: {
        lastUsedAt: 'desc'
      },
      take: 5,
      select: {
        id: true,
        status: true,
        lastUsedAt: true,
        createdAt: true
      }
    })

    const result = {
      id: pool.id,
      name: pool.name,
      poolType: {
        id: pool.poolType.id,
        name: pool.poolType.name
      },
      stats,
      recentActivity: recentAccounts,
      createdAt: pool.createdAt || new Date(),
      updatedAt: pool.updatedAt || new Date()
    }

    return {
      success: true,
      data: result
    }
  } catch (error) {
    console.error('Failed to get pool details:', error)
    
    // 如果是已知的错误（如验证错误），直接抛出
    if (error.statusCode) {
      throw error
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to get pool details'
    })
  }
})
