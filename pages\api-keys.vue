<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 页面头部 -->
    <div class="bg-white shadow">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="py-6">
          <!-- 面包屑导航 -->
          <nav class="flex items-center space-x-2 text-sm">
            <NuxtLink to="/" class="text-gray-500 hover:text-gray-700">首页</NuxtLink>
            <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
            <span class="text-gray-900 font-medium">API密钥管理</span>
          </nav>
        </div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- 统计信息 -->
      <div class="bg-white rounded-lg shadow p-6 mb-8">
        <h2 class="text-lg font-medium text-gray-900 mb-6">统计概览</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div class="text-center p-4 bg-blue-50 rounded-lg">
            <div class="text-2xl font-bold text-blue-600">{{ stats.total }}</div>
            <div class="text-sm text-gray-600">总密钥数</div>
          </div>
          <div class="text-center p-4 bg-green-50 rounded-lg">
            <div class="text-2xl font-bold text-green-600">{{ stats.active }}</div>
            <div class="text-sm text-gray-600">启用中</div>
          </div>
          <div class="text-center p-4 bg-gray-50 rounded-lg">
            <div class="text-2xl font-bold text-gray-600">{{ stats.disabled }}</div>
            <div class="text-sm text-gray-600">已禁用</div>
          </div>
          <div class="text-center p-4 bg-red-50 rounded-lg">
            <div class="text-2xl font-bold text-red-600">{{ stats.expired }}</div>
            <div class="text-sm text-gray-600">已过期</div>
          </div>
        </div>
      </div>

      <!-- API密钥列表 -->
      <div class="bg-white shadow overflow-hidden sm:rounded-md">
        <div class="px-4 py-5 sm:px-6 border-b border-gray-200">
          <div class="flex justify-between items-center">
            <h3 class="text-lg leading-6 font-medium text-gray-900">API密钥列表</h3>
            <div class="text-sm text-gray-500">共 {{ apiKeys.length }} 个密钥</div>
          </div>
        </div>

        <div class="p-6">
          <!-- 操作按钮 -->
          <div class="mb-4 flex justify-between items-center">
            <div class="flex space-x-3">
              <button
                @click="showCreateModal = true"
                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                创建密钥
              </button>
              <button
                @click="showBatchCreateModal = true"
                class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                批量创建
              </button>
            </div>
          </div>

          <ApiKeyTable
            :api-keys="apiKeys"
            :loading="loading"
            @refresh="loadData"
            @edit-api-key="editApiKey"
            @delete-api-key="deleteApiKey"
            @toggle-status="toggleApiKeyStatus"
            @copy-api-key="copyApiKey"
            @batch-delete="handleBatchDelete"
            @batch-update-status="handleBatchUpdateStatus"
          />
        </div>
      </div>
    </div>

    <!-- 创建/编辑API密钥模态框 -->
    <div v-if="showCreateModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 w-full max-w-md">
        <h3 class="text-lg font-medium mb-4">{{ editingApiKey ? '编辑API密钥' : '创建API密钥' }}</h3>
        <form @submit.prevent="handleSubmit">
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">密钥名称</label>
              <input
                v-model="form.name"
                type="text"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="请输入密钥名称"
              >
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">描述</label>
              <textarea
                v-model="form.description"
                rows="3"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="请输入描述信息（可选）"
              ></textarea>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">关联用户（可选）</label>
              <select
                v-model="form.userId"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">不关联用户</option>
                <option v-for="user in users" :key="user.id" :value="user.id">
                  {{ user.name }} {{ user.email ? `(${user.email})` : '' }}
                </option>
              </select>
              <p class="mt-1 text-sm text-gray-500">选择要关联的用户</p>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">权限配置</label>
              <div class="space-y-2 max-h-40 overflow-y-auto border border-gray-200 rounded-md p-3">
                <div v-for="permission in availablePermissions" :key="permission.value" class="flex items-center">
                  <input
                    :id="`permission-${permission.value}`"
                    v-model="form.permissions"
                    type="checkbox"
                    :value="permission.value"
                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  >
                  <label :for="`permission-${permission.value}`" class="ml-2 text-sm text-gray-700">
                    {{ permission.label }}
                  </label>
                </div>
              </div>
              <p class="mt-1 text-sm text-gray-500">选择API密钥的权限</p>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">过期时间（可选）</label>
              <input
                v-model="form.expiresAt"
                type="datetime-local"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
              <p class="mt-1 text-sm text-gray-500">留空表示永不过期</p>
            </div>
          </div>
          <div class="flex justify-end space-x-3 mt-6">
            <button
              type="button"
              @click="closeModal"
              class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
            >
              取消
            </button>
            <button
              type="submit"
              :disabled="!form.name.trim()"
              class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              {{ editingApiKey ? '更新' : '创建' }}
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- 批量创建API密钥模态框 -->
    <div v-if="showBatchCreateModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <h3 class="text-lg font-medium mb-4">批量创建API密钥</h3>
        <form @submit.prevent="handleBatchSubmit">
          <div class="space-y-4">
            <!-- 基础配置 -->
            <div class="grid grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">密钥前缀</label>
                <input
                  v-model="batchForm.namePrefix"
                  type="text"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="例如: project-api"
                >
                <p class="mt-1 text-xs text-gray-500">将自动添加序号，如: project-api-001</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">创建数量</label>
                <input
                  v-model.number="batchForm.count"
                  type="number"
                  min="1"
                  max="100"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="1-100"
                >
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">描述模板</label>
              <textarea
                v-model="batchForm.descriptionTemplate"
                rows="2"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="批量创建的API密钥 - {序号}"
              ></textarea>
              <p class="mt-1 text-xs text-gray-500">使用 {序号} 作为占位符</p>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">关联用户（可选）</label>
              <select
                v-model="batchForm.userId"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">不关联用户</option>
                <option v-for="user in users" :key="user.id" :value="user.id">
                  {{ user.name }} {{ user.email ? `(${user.email})` : '' }}
                </option>
              </select>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">权限配置</label>
              <div class="space-y-2 max-h-32 overflow-y-auto border border-gray-200 rounded-md p-3">
                <div v-for="permission in availablePermissions" :key="permission.value" class="flex items-center">
                  <input
                    :id="`batch-permission-${permission.value}`"
                    v-model="batchForm.permissions"
                    type="checkbox"
                    :value="permission.value"
                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  >
                  <label :for="`batch-permission-${permission.value}`" class="ml-2 text-sm text-gray-700">
                    {{ permission.label }}
                  </label>
                </div>
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">过期时间（可选）</label>
              <input
                v-model="batchForm.expiresAt"
                type="datetime-local"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
              <p class="mt-1 text-sm text-gray-500">留空表示永不过期</p>
            </div>

            <!-- 预览 -->
            <div v-if="batchForm.namePrefix && batchForm.count > 0" class="bg-gray-50 rounded-md p-3">
              <h4 class="text-sm font-medium text-gray-700 mb-2">预览（前5个）:</h4>
              <div class="space-y-1 text-xs text-gray-600">
                <div v-for="i in Math.min(batchForm.count, 5)" :key="i">
                  {{ batchForm.namePrefix }}-{{ String(i).padStart(3, '0') }}
                </div>
                <div v-if="batchForm.count > 5" class="text-gray-400">
                  ... 还有 {{ batchForm.count - 5 }} 个
                </div>
              </div>
            </div>
          </div>

          <div class="flex justify-end space-x-3 mt-6">
            <button
              type="button"
              @click="closeBatchModal"
              class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
            >
              取消
            </button>
            <button
              type="submit"
              :disabled="!batchForm.namePrefix.trim() || !batchForm.count || batchForm.count < 1 || batchForm.count > 100 || batchSubmitting"
              class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              {{ batchSubmitting ? '创建中...' : `创建 ${batchForm.count} 个密钥` }}
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- 提示信息 -->
    <div
      v-if="showToast"
      class="fixed top-4 right-4 z-50 max-w-sm w-full bg-white border border-gray-200 rounded-lg shadow-lg"
      :class="{
        'border-green-200 bg-green-50': toastType === 'success',
        'border-red-200 bg-red-50': toastType === 'error',
        'border-blue-200 bg-blue-50': toastType === 'info'
      }"
    >
      <div class="p-4">
        <div class="flex items-start">
          <div class="flex-shrink-0">
            <!-- 成功图标 -->
            <svg
              v-if="toastType === 'success'"
              class="w-5 h-5 text-green-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <!-- 错误图标 -->
            <svg
              v-else-if="toastType === 'error'"
              class="w-5 h-5 text-red-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <!-- 信息图标 -->
            <svg
              v-else
              class="w-5 h-5 text-blue-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div class="ml-3 w-0 flex-1">
            <p
              class="text-sm font-medium"
              :class="{
                'text-green-800': toastType === 'success',
                'text-red-800': toastType === 'error',
                'text-blue-800': toastType === 'info'
              }"
            >
              {{ toastMessage }}
            </p>
          </div>
          <div class="ml-4 flex-shrink-0 flex">
            <button
              @click="hideToast"
              class="inline-flex text-gray-400 hover:text-gray-500"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// 页面元数据
definePageMeta({
  title: 'API密钥管理 - EasyPool'
})

// 设置页面标题
useHead({
  title: 'API密钥管理 - EasyPool'
})

// 响应式数据
const loading = ref(true)
const apiKeys = ref([])
const users = ref([])
const showCreateModal = ref(false)
const editingApiKey = ref(null)
const form = ref({
  name: '',
  description: '',
  expiresAt: '',
  userId: '',
  permissions: []
})

// 批量创建相关
const showBatchCreateModal = ref(false)
const batchSubmitting = ref(false)
const batchForm = ref({
  namePrefix: '',
  count: 10,
  descriptionTemplate: '批量创建的API密钥 - {序号}',
  userId: '',
  permissions: [],
  expiresAt: ''
})

// 可用权限列表
const availablePermissions = ref([
  { value: 'account:read', label: '读取账号' },
  { value: 'account:create', label: '创建账号' },
  { value: 'account:update', label: '更新账号' },
  { value: 'account:delete', label: '删除账号' },
  { value: 'account:batch', label: '批量操作账号' },
  { value: 'pool:read', label: '读取号池' },
  { value: 'pool:create', label: '创建号池' },
  { value: 'pool:update', label: '更新号池' },
  { value: 'pool:delete', label: '删除号池' },
  { value: 'stats:read', label: '读取统计信息' }
])

// 提示信息相关
const showToast = ref(false)
const toastMessage = ref('')
const toastType = ref('success') // success, error, info
let toastTimer = null

// 计算属性
const stats = computed(() => {
  if (!apiKeys.value || apiKeys.value.length === 0) {
    return { total: 0, active: 0, disabled: 0, expired: 0 }
  }
  
  const stats = {
    total: apiKeys.value.length,
    active: 0,
    disabled: 0,
    expired: 0
  }
  
  apiKeys.value.forEach(apiKey => {
    const status = getActualStatus(apiKey)
    switch (status) {
      case 'Active':
        stats.active++
        break
      case 'Disabled':
        stats.disabled++
        break
      case 'Expired':
        stats.expired++
        break
    }
  })
  
  return stats
})

// 提示信息方法
const showToastMessage = (message, type = 'success', duration = 3000) => {
  toastMessage.value = message
  toastType.value = type
  showToast.value = true

  // 清除之前的定时器
  if (toastTimer) {
    clearTimeout(toastTimer)
  }

  // 设置自动隐藏
  toastTimer = setTimeout(() => {
    hideToast()
  }, duration)
}

const hideToast = () => {
  showToast.value = false
  if (toastTimer) {
    clearTimeout(toastTimer)
    toastTimer = null
  }
}

// 方法
const formatDate = (dateString) => {
  if (!dateString) return '未知'
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN')
}

const isExpired = (apiKey) => {
  if (!apiKey.expiresAt) return false
  return new Date(apiKey.expiresAt) < new Date()
}

const getActualStatus = (apiKey) => {
  if (isExpired(apiKey)) return 'Expired'
  return apiKey.status
}

const getStatusText = (apiKey) => {
  const status = getActualStatus(apiKey)
  switch (status) {
    case 'Active': return '启用中'
    case 'Disabled': return '已禁用'
    case 'Expired': return '已过期'
    default: return '未知'
  }
}

const getStatusClass = (apiKey) => {
  const status = getActualStatus(apiKey)
  switch (status) {
    case 'Active': return 'bg-green-100 text-green-800'
    case 'Disabled': return 'bg-gray-100 text-gray-800'
    case 'Expired': return 'bg-red-100 text-red-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

const loadData = async () => {
  try {
    loading.value = true

    // 并行加载API密钥和用户数据
    const [apiKeysResponse, usersResponse] = await Promise.all([
      $fetch('/api/admin/api-keys'),
      $fetch('/api/admin/users')
    ])

    if (apiKeysResponse.success) {
      apiKeys.value = apiKeysResponse.data
    }

    if (usersResponse.success) {
      users.value = usersResponse.data.users
    }
  } catch (error) {
    console.error('Failed to load data:', error)
  } finally {
    loading.value = false
  }
}

const editApiKey = (apiKey) => {
  editingApiKey.value = apiKey

  // 解析权限
  let permissions = []
  if (apiKey.permissions) {
    try {
      permissions = JSON.parse(apiKey.permissions)
    } catch (error) {
      console.error('Error parsing permissions:', error)
    }
  }

  form.value = {
    name: apiKey.name,
    description: apiKey.description || '',
    expiresAt: apiKey.expiresAt ? new Date(apiKey.expiresAt).toISOString().slice(0, 16) : '',
    userId: apiKey.userId || '',
    permissions
  }
  showCreateModal.value = true
}

const closeModal = () => {
  showCreateModal.value = false
  editingApiKey.value = null
  form.value = {
    name: '',
    description: '',
    expiresAt: '',
    userId: '',
    permissions: []
  }
}

// 批量创建相关方法
const closeBatchModal = () => {
  showBatchCreateModal.value = false
  batchForm.value = {
    namePrefix: '',
    count: 10,
    descriptionTemplate: '批量创建的API密钥 - {序号}',
    userId: '',
    permissions: [],
    expiresAt: ''
  }
}

const handleBatchSubmit = async () => {
  if (batchSubmitting.value) return

  try {
    batchSubmitting.value = true

    // 准备批量创建的数据
    const apiKeysToCreate = []
    for (let i = 1; i <= batchForm.value.count; i++) {
      const paddedNumber = String(i).padStart(3, '0')
      const name = `${batchForm.value.namePrefix}-${paddedNumber}`
      const description = batchForm.value.descriptionTemplate.replace('{序号}', paddedNumber)

      apiKeysToCreate.push({
        name,
        description,
        expiresAt: batchForm.value.expiresAt ? new Date(batchForm.value.expiresAt).toISOString() : null,
        userId: batchForm.value.userId || null,
        permissions: batchForm.value.permissions.length > 0 ? batchForm.value.permissions : null
      })
    }

    // 调用批量创建API
    const result = await $fetch('/api/admin/api-keys/batch', {
      method: 'POST',
      body: { apiKeys: apiKeysToCreate }
    })

    // 生成下载文件
    if (result.success && result.apiKeys) {
      downloadApiKeysAsText(result.apiKeys)
      showToastMessage(`成功创建 ${result.apiKeys.length} 个API密钥并已下载`, 'success')
    } else {
      showToastMessage('批量创建完成，但部分密钥可能创建失败', 'info')
    }

    closeBatchModal()
    await loadData()
  } catch (error) {
    console.error('Failed to batch create API keys:', error)
    showToastMessage('批量创建API密钥失败，请重试', 'error')
  } finally {
    batchSubmitting.value = false
  }
}

// 下载API密钥为文本文件
const downloadApiKeysAsText = (apiKeys) => {
  const content = apiKeys.map(apiKey => {
    return `名称: ${apiKey.name}
密钥: ${apiKey.key}
描述: ${apiKey.description || '无'}
权限: ${apiKey.permissions ? JSON.parse(apiKey.permissions).join(', ') : '无'}
过期时间: ${apiKey.expiresAt ? new Date(apiKey.expiresAt).toLocaleString() : '永不过期'}
创建时间: ${new Date(apiKey.createdAt).toLocaleString()}
${'='.repeat(50)}`
  }).join('\n\n')

  const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `api-keys-${new Date().toISOString().slice(0, 10)}.txt`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
}

const handleSubmit = async () => {
  try {
    const data = {
      name: form.value.name,
      description: form.value.description,
      expiresAt: form.value.expiresAt ? new Date(form.value.expiresAt).toISOString() : null,
      userId: form.value.userId || null,
      permissions: form.value.permissions.length > 0 ? form.value.permissions : null
    }

    if (editingApiKey.value) {
      // 更新API密钥
      await $fetch(`/api/admin/api-keys/${editingApiKey.value.id}`, {
        method: 'PUT',
        body: data
      })
      showToastMessage(`API密钥 "${data.name}" 已更新`, 'success')
    } else {
      // 创建API密钥
      await $fetch('/api/admin/api-keys', {
        method: 'POST',
        body: data
      })
      showToastMessage(`API密钥 "${data.name}" 已创建`, 'success')
    }

    closeModal()
    await loadData()
  } catch (error) {
    console.error('Failed to save API key:', error)
    const action = editingApiKey.value ? '更新' : '创建'
    showToastMessage(`${action}API密钥失败，请重试`, 'error')
  }
}

const toggleApiKeyStatus = async (apiKey, newStatus) => {
  try {
    await $fetch(`/api/admin/api-keys/${apiKey.id}/status`, {
      method: 'PUT',
      body: { status: newStatus }
    })

    const statusText = newStatus === 'Active' ? '启用' : '禁用'
    showToastMessage(`API密钥 "${apiKey.name}" 已${statusText}`, 'success')

    await loadData()
  } catch (error) {
    console.error('Failed to toggle API key status:', error)
    const statusText = newStatus === 'Active' ? '启用' : '禁用'
    showToastMessage(`${statusText}API密钥失败，请重试`, 'error')
  }
}

const deleteApiKey = async (apiKey) => {
  if (confirm(`确定要删除API密钥 "${apiKey.name}" 吗？`)) {
    try {
      await $fetch(`/api/admin/api-keys/${apiKey.id}`, {
        method: 'DELETE'
      })
      showToastMessage(`API密钥 "${apiKey.name}" 已删除`, 'success')
      await loadData()
    } catch (error) {
      console.error('Failed to delete API key:', error)
      showToastMessage('删除API密钥失败，请重试', 'error')
    }
  }
}

const copyApiKey = async (apiKey) => {
  try {
    await navigator.clipboard.writeText(apiKey.key)
    showToastMessage('API密钥已复制到剪贴板', 'success')
  } catch (error) {
    console.error('Failed to copy API key:', error)
    showToastMessage('复制失败，请手动复制', 'error')
  }
}

// 批量操作处理方法
const handleBatchDelete = async (apiKeyIds) => {
  try {
    const response = await $fetch('/api/admin/api-keys/batch-delete', {
      method: 'POST',
      body: { apiKeyIds }
    })

    if (response.success) {
      showToastMessage(`批量删除成功：${response.data.deletedCount} 个API密钥`, 'success')
      await loadData()
    }
  } catch (error) {
    console.error('Failed to batch delete API keys:', error)
    showToastMessage('批量删除失败，请重试', 'error')
  }
}

const handleBatchUpdateStatus = async (apiKeyIds, status) => {
  try {
    const response = await $fetch('/api/admin/api-keys/batch-status', {
      method: 'PUT',
      body: { apiKeyIds, status }
    })

    if (response.success) {
      const statusText = status === 'Active' ? '启用' : '禁用'
      showToastMessage(`批量${statusText}成功：${response.data.updatedCount} 个API密钥`, 'success')
      await loadData()
    }
  } catch (error) {
    console.error('Failed to batch update API key status:', error)
    const statusText = status === 'Active' ? '启用' : '禁用'
    showToastMessage(`批量${statusText}失败，请重试`, 'error')
  }
}

// 页面加载时获取数据
onMounted(() => {
  loadData()
})
</script>
