/**
 * API拦截器插件
 * 拦截所有API请求，添加性能监控、错误处理、重试机制等
 */

export default defineNuxtPlugin(() => {
  const { $performance } = useNuxtApp()

  // API配置
  const config = {
    timeout: 30000, // 30秒超时
    retryAttempts: 3, // 重试次数
    retryDelay: 1000, // 重试延迟（毫秒）
    enableLogging: process.env.NODE_ENV === 'development'
  }

  // 请求计数器
  let requestCounter = 0
  const activeRequests = new Map<string, number>()

  // 生成请求ID
  const generateRequestId = () => {
    return `req_${++requestCounter}_${Date.now()}`
  }

  // 延迟函数
  const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

  // 重试函数
  const retryRequest = async (
    url: string,
    options: any,
    attempt: number = 1
  ): Promise<any> => {
    try {
      const response = await $fetch(url, options)
      return response
    } catch (error: any) {
      if (attempt < config.retryAttempts && shouldRetry(error)) {
        console.warn(`Request failed (attempt ${attempt}/${config.retryAttempts}), retrying in ${config.retryDelay}ms...`)
        await delay(config.retryDelay * attempt) // 指数退避
        return retryRequest(url, options, attempt + 1)
      }
      throw error
    }
  }

  // 判断是否应该重试
  const shouldRetry = (error: any): boolean => {
    // 网络错误或5xx服务器错误可以重试
    if (!error.response) return true // 网络错误
    const status = error.response.status
    return status >= 500 && status < 600 // 5xx错误
  }

  // 请求拦截器
  const requestInterceptor = (url: string, options: any = {}) => {
    const requestId = generateRequestId()
    const startTime = performance.now()

    // 记录活跃请求
    activeRequests.set(requestId, startTime)

    // 添加默认配置
    const enhancedOptions = {
      timeout: config.timeout,
      ...options,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      }
    }

    // 日志记录
    if (config.enableLogging) {
      console.group(`🚀 API Request: ${options.method || 'GET'} ${url}`)
      console.log('Request ID:', requestId)
      console.log('Options:', enhancedOptions)
      console.log('Start Time:', new Date().toISOString())
      console.groupEnd()
    }

    return { requestId, startTime, enhancedOptions }
  }

  // 响应拦截器
  const responseInterceptor = (
    requestId: string,
    url: string,
    method: string,
    startTime: number,
    response: any,
    error?: any
  ) => {
    const endTime = performance.now()
    const duration = endTime - startTime

    // 移除活跃请求记录
    activeRequests.delete(requestId)

    // 记录API性能
    if ($performance?.recordApiCall) {
      $performance.recordApiCall(
        url,
        method || 'GET',
        startTime,
        endTime,
        error ? (error.response?.status || 0) : 200
      )
    }

    // 日志记录
    if (config.enableLogging) {
      const logType = error ? '❌ API Error' : '✅ API Success'
      console.group(`${logType}: ${method || 'GET'} ${url}`)
      console.log('Request ID:', requestId)
      console.log('Duration:', `${duration.toFixed(2)}ms`)
      
      if (error) {
        console.log('Error:', error)
        console.log('Status:', error.response?.status || 'Network Error')
      } else {
        console.log('Response:', response)
      }
      
      console.log('End Time:', new Date().toISOString())
      console.groupEnd()
    }

    // 错误处理
    if (error) {
      handleApiError(error, url, method || 'GET', duration)
    }

    return { response, error, duration }
  }

  // API错误处理
  const handleApiError = (error: any, url: string, method: string, duration: number) => {
    const errorInfo = {
      url,
      method,
      duration,
      status: error.response?.status || 0,
      message: error.message || 'Unknown error',
      timestamp: new Date().toISOString()
    }

    // 根据错误类型进行不同处理
    if (!error.response) {
      // 网络错误
      console.error('Network Error:', errorInfo)
    } else {
      const status = error.response.status
      
      switch (status) {
        case 401:
          // 未授权，可能需要重新登录
          console.warn('Unauthorized access:', errorInfo)
          break
        case 403:
          // 禁止访问
          console.warn('Forbidden access:', errorInfo)
          break
        case 404:
          // 资源未找到
          console.warn('Resource not found:', errorInfo)
          break
        case 429:
          // 请求过于频繁
          console.warn('Rate limit exceeded:', errorInfo)
          break
        case 500:
        case 502:
        case 503:
        case 504:
          // 服务器错误
          console.error('Server error:', errorInfo)
          break
        default:
          console.error('API Error:', errorInfo)
      }
    }

    // 在生产环境中，可以将错误信息发送到错误监控服务
    if (process.env.NODE_ENV === 'production') {
      // 示例：发送到错误监控服务
      /*
      fetch('/api/errors', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(errorInfo)
      }).catch(() => {
        // 忽略错误上报失败
      })
      */
    }
  }

  // 增强$fetch函数
  const enhancedFetch = async (url: string, options: any = {}) => {
    const { requestId, startTime, enhancedOptions } = requestInterceptor(url, options)
    
    try {
      // 使用重试机制
      const response = await retryRequest(url, enhancedOptions)
      
      responseInterceptor(requestId, url, options.method, startTime, response)
      return response
    } catch (error) {
      responseInterceptor(requestId, url, options.method, startTime, null, error)
      throw error
    }
  }

  // 监控活跃请求
  if (process.client) {
    // 定期检查长时间运行的请求
    setInterval(() => {
      const now = performance.now()
      const longRunningThreshold = 10000 // 10秒

      activeRequests.forEach((startTime, requestId) => {
        const duration = now - startTime
        if (duration > longRunningThreshold) {
          console.warn(`Long running request detected: ${requestId} (${duration.toFixed(2)}ms)`)
        }
      })
    }, 5000) // 每5秒检查一次

    // 页面卸载时取消所有请求
    window.addEventListener('beforeunload', () => {
      if (activeRequests.size > 0) {
        console.log(`Cancelling ${activeRequests.size} active requests`)
        activeRequests.clear()
      }
    })
  }

  // 提供增强的fetch函数
  return {
    provide: {
      enhancedFetch,
      apiStats: {
        getActiveRequestCount: () => activeRequests.size,
        getActiveRequests: () => Array.from(activeRequests.keys())
      }
    }
  }
})
