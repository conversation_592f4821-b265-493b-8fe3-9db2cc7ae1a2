/**
 * 创建号池接口
 * POST /api/admin/pools
 */

import { prisma } from '../../utils/db'

export default defineEventHandler(async (event) => {
  try {
    // 获取请求体数据
    const body = await readBody(event)
    
    // 数据验证
    if (!body || !body.name || !body.poolTypeId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Pool name and pool type ID are required'
      })
    }

    const { name, description, poolTypeId } = body

    // 验证名称格式
    if (typeof name !== 'string' || name.trim().length === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Pool name must be a non-empty string'
      })
    }

    const trimmedName = name.trim()

    // 验证名称长度
    if (trimmedName.length > 50) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Pool name must be 50 characters or less'
      })
    }

    // 验证描述长度（如果提供）
    if (description && typeof description === 'string' && description.length > 1000) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Pool description must be 1000 characters or less'
      })
    }

    // 验证号池类型ID格式
    if (typeof poolTypeId !== 'string' || poolTypeId.length < 10) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid pool type ID format'
      })
    }

    // 检查号池类型是否存在
    const poolType = await prisma.poolType.findUnique({
      where: { id: poolTypeId }
    })

    if (!poolType) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Pool type not found'
      })
    }

    // 检查号池名称是否已存在
    const existingPool = await prisma.pool.findUnique({
      where: { name: trimmedName }
    })

    if (existingPool) {
      throw createError({
        statusCode: 409,
        statusMessage: 'Pool with this name already exists'
      })
    }

    // 创建新的号池
    const newPool = await prisma.pool.create({
      data: {
        name: trimmedName,
        description: description ? description.trim() : null,
        poolTypeId
      },
      include: {
        poolType: true
      }
    })

    console.log(`New pool created: ${newPool.name} (ID: ${newPool.id}) in type: ${poolType.name}`)

    return {
      success: true,
      message: 'Pool created successfully',
      data: {
        id: newPool.id,
        name: newPool.name,
        description: newPool.description,
        poolType: {
          id: newPool.poolType.id,
          name: newPool.poolType.name
        },
        stats: {
          total: 0,
          available: 0,
          inUse: 0,
          invalid: 0,
          expired: 0
        },
        createdAt: new Date()
      }
    }
  } catch (error) {
    console.error('Failed to create pool:', error)
    
    // 如果是已知的错误（如验证错误），直接抛出
    if (error.statusCode) {
      throw error
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to create pool'
    })
  }
})
