# 数据库配置
# Database Configuration
DATABASE_URL="mysql://username:password@host:port/database"

# 应用配置
# Application Configuration
NUXT_SECRET_KEY="your-secret-key-minimum-32-characters-long"
NUXT_PUBLIC_APP_URL="http://localhost:3000"

# 环境配置
# Environment Configuration
NODE_ENV="development"
PORT=3000

# 日志配置
# Logging Configuration
LOG_LEVEL="info"

# API配置
# API Configuration
API_RATE_LIMIT_MAX=100
API_RATE_LIMIT_WINDOW=900000

# 会话配置
# Session Configuration
SESSION_TIMEOUT=3600000

# 数据库连接示例
# Database Connection Examples

# TiDB Cloud (推荐)
# DATABASE_URL="mysql://username:<EMAIL>:4000/database?sslaccept=strict"

# MySQL 本地
# DATABASE_URL="mysql://root:password@localhost:3306/qnb_pool"

# MySQL 远程
# DATABASE_URL="mysql://username:password@your-mysql-host:3306/qnb_pool"

# 生产环境示例
# Production Environment Example
# NODE_ENV="production"
# NUXT_PUBLIC_APP_URL="https://your-domain.com"
# LOG_LEVEL="warn"
