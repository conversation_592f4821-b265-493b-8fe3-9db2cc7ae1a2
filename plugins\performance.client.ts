/**
 * 性能监控插件
 * 监控页面加载性能、API响应时间等关键指标
 */

export default defineNuxtPlugin(() => {
  // 性能监控配置
  const config = {
    enableLogging: process.env.NODE_ENV === 'development',
    enableReporting: process.env.NODE_ENV === 'production',
    apiThreshold: 2000, // API响应时间阈值（毫秒）
    pageLoadThreshold: 3000 // 页面加载时间阈值（毫秒）
  }

  // 性能数据收集器
  const performanceCollector = {
    // 记录页面加载性能
    recordPageLoad() {
      if (typeof window === 'undefined') return

      window.addEventListener('load', () => {
        setTimeout(() => {
          const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
          
          if (navigation) {
            const metrics = {
              // 页面加载时间
              loadTime: navigation.loadEventEnd - navigation.fetchStart,
              // DNS查询时间
              dnsTime: navigation.domainLookupEnd - navigation.domainLookupStart,
              // TCP连接时间
              tcpTime: navigation.connectEnd - navigation.connectStart,
              // 请求响应时间
              requestTime: navigation.responseEnd - navigation.requestStart,
              // DOM解析时间
              domParseTime: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
              // 资源加载时间
              resourceTime: navigation.loadEventEnd - navigation.domContentLoadedEventEnd
            }

            this.logMetrics('Page Load', metrics)
            
            // 检查性能阈值
            if (metrics.loadTime > config.pageLoadThreshold) {
              console.warn(`Page load time (${metrics.loadTime}ms) exceeds threshold (${config.pageLoadThreshold}ms)`)
            }
          }
        }, 0)
      })
    },

    // 记录API性能
    recordApiCall(url: string, method: string, startTime: number, endTime: number, status: number) {
      const duration = endTime - startTime
      
      const metrics = {
        url,
        method,
        duration,
        status,
        timestamp: new Date().toISOString()
      }

      this.logMetrics('API Call', metrics)
      
      // 检查API响应时间阈值
      if (duration > config.apiThreshold) {
        console.warn(`API call (${method} ${url}) took ${duration}ms, exceeds threshold (${config.apiThreshold}ms)`)
      }
    },

    // 记录用户交互性能
    recordInteraction(action: string, startTime: number, endTime: number) {
      const duration = endTime - startTime
      
      const metrics = {
        action,
        duration,
        timestamp: new Date().toISOString()
      }

      this.logMetrics('User Interaction', metrics)
    },

    // 记录内存使用情况
    recordMemoryUsage() {
      if (typeof window === 'undefined' || !('memory' in performance)) return

      const memory = (performance as any).memory
      
      const metrics = {
        usedJSHeapSize: memory.usedJSHeapSize,
        totalJSHeapSize: memory.totalJSHeapSize,
        jsHeapSizeLimit: memory.jsHeapSizeLimit,
        usagePercentage: Math.round((memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100)
      }

      this.logMetrics('Memory Usage', metrics)
      
      // 检查内存使用率
      if (metrics.usagePercentage > 80) {
        console.warn(`Memory usage (${metrics.usagePercentage}%) is high`)
      }
    },

    // 日志输出
    logMetrics(type: string, metrics: any) {
      if (config.enableLogging) {
        console.group(`🔍 Performance: ${type}`)
        console.table(metrics)
        console.groupEnd()
      }

      if (config.enableReporting) {
        // 在生产环境中，可以将性能数据发送到监控服务
        this.reportMetrics(type, metrics)
      }
    },

    // 上报性能数据
    reportMetrics(type: string, metrics: any) {
      // 这里可以集成第三方监控服务，如 Google Analytics、Sentry 等
      // 示例：发送到自定义监控端点
      /*
      fetch('/api/performance', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          type,
          metrics,
          userAgent: navigator.userAgent,
          url: window.location.href,
          timestamp: new Date().toISOString()
        })
      }).catch(err => {
        console.warn('Failed to report performance metrics:', err)
      })
      */
    }
  }

  // 初始化性能监控
  if (process.client) {
    // 监控页面加载性能
    performanceCollector.recordPageLoad()

    // 定期监控内存使用情况
    setInterval(() => {
      performanceCollector.recordMemoryUsage()
    }, 30000) // 每30秒检查一次

    // 监控长任务
    if ('PerformanceObserver' in window) {
      try {
        const longTaskObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (entry.duration > 50) { // 长任务阈值：50ms
              console.warn(`Long task detected: ${entry.duration}ms`)
              performanceCollector.logMetrics('Long Task', {
                duration: entry.duration,
                startTime: entry.startTime,
                name: entry.name
              })
            }
          }
        })
        
        longTaskObserver.observe({ entryTypes: ['longtask'] })
      } catch (e) {
        console.warn('Long task monitoring not supported')
      }
    }

    // 监控资源加载
    if ('PerformanceObserver' in window) {
      try {
        const resourceObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            const resource = entry as PerformanceResourceTiming
            
            // 监控慢资源
            if (resource.duration > 1000) { // 资源加载超过1秒
              console.warn(`Slow resource: ${resource.name} took ${resource.duration}ms`)
              performanceCollector.logMetrics('Slow Resource', {
                name: resource.name,
                duration: resource.duration,
                size: resource.transferSize,
                type: resource.initiatorType
              })
            }
          }
        })
        
        resourceObserver.observe({ entryTypes: ['resource'] })
      } catch (e) {
        console.warn('Resource monitoring not supported')
      }
    }
  }

  // 提供全局性能监控方法
  return {
    provide: {
      performance: {
        recordApiCall: performanceCollector.recordApiCall.bind(performanceCollector),
        recordInteraction: performanceCollector.recordInteraction.bind(performanceCollector),
        recordMemoryUsage: performanceCollector.recordMemoryUsage.bind(performanceCollector)
      }
    }
  }
})
