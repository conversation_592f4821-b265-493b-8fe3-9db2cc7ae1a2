version: '3.8'

services:
  # 开发环境应用服务
  app:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - DATABASE_URL=mysql://qnb_user:qnb_password@mysql:3306/qnb_pool
      - NUXT_SECRET_KEY=development-secret-key-minimum-32-characters
      - NUXT_PUBLIC_APP_URL=http://localhost:3000
    volumes:
      - .:/app
      - /app/node_modules
      - /app/.nuxt
      - /app/.output
    depends_on:
      mysql:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - qnb-dev-network
    command: npm run dev

  # MySQL 数据库服务
  mysql:
    image: mysql:8.0
    ports:
      - "3306:3306"
    environment:
      - MYSQL_ROOT_PASSWORD=root_password
      - MYSQL_DATABASE=qnb_pool
      - MYSQL_USER=qnb_user
      - MYSQL_PASSWORD=qnb_password
    volumes:
      - mysql_dev_data:/var/lib/mysql
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10
    restart: unless-stopped
    networks:
      - qnb-dev-network

  # phpMyAdmin 数据库管理工具
  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    ports:
      - "8080:80"
    environment:
      - PMA_HOST=mysql
      - PMA_USER=qnb_user
      - PMA_PASSWORD=qnb_password
    depends_on:
      - mysql
    restart: unless-stopped
    networks:
      - qnb-dev-network

  # Redis 缓存服务
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_dev_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      timeout: 3s
      retries: 5
    restart: unless-stopped
    networks:
      - qnb-dev-network

  # Redis Commander 管理工具
  redis-commander:
    image: rediscommander/redis-commander:latest
    ports:
      - "8081:8081"
    environment:
      - REDIS_HOSTS=local:redis:6379
    depends_on:
      - redis
    restart: unless-stopped
    networks:
      - qnb-dev-network

volumes:
  mysql_dev_data:
    driver: local
  redis_dev_data:
    driver: local

networks:
  qnb-dev-network:
    driver: bridge
