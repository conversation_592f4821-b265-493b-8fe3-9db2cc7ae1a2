/**
 * 获取当前管理员信息接口
 * GET /api/admin/auth/me
 */

import { validateAdminSession } from '../../../utils/auth'

export default defineEventHandler(async (event) => {
  try {
    const token = getCookie(event, 'admin_token')
    
    if (!token) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Not authenticated'
      })
    }

    const session = await validateAdminSession(token)
    
    if (!session) {
      // 清除无效的cookie
      deleteCookie(event, 'admin_token')
      
      throw createError({
        statusCode: 401,
        statusMessage: 'Invalid or expired session'
      })
    }

    return {
      success: true,
      data: session.admin
    }
  } catch (error) {
    console.error('Get admin info error:', error)
    
    if (error.statusCode) {
      throw error
    }

    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error'
    })
  }
})
