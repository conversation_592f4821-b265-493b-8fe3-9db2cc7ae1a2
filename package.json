{"name": "easy-pool", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "db:push": "prisma db push", "db:generate": "prisma generate"}, "dependencies": {"@nuxtjs/tailwindcss": "^6.12.1", "@pinia/nuxt": "^0.5.5", "@prisma/client": "^5.22.0", "bcryptjs": "^3.0.2", "nuxt": "^4.0.1", "pinia": "^2.2.6", "vue": "^3.5.18", "vue-router": "^4.5.1"}, "devDependencies": {"@types/bcryptjs": "^3.0.0", "@types/node": "^22.10.2", "prisma": "^5.22.0", "tailwindcss": "^3.4.15", "typescript": "^5.8.3", "vue-tsc": "^3.0.4"}}