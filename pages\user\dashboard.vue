<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 顶部导航 -->
    <nav class="bg-white shadow">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex items-center">
            <div class="flex-shrink-0 flex items-center">
              <div class="w-8 h-8 bg-gradient-to-r from-green-600 to-blue-600 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/>
                </svg>
              </div>
              <h1 class="ml-3 text-xl font-semibold text-gray-900">EasyPool</h1>
            </div>
          </div>
          <div class="flex items-center space-x-4">
            <span class="text-sm text-gray-700">{{ user?.name }}</span>
            <button
              @click="handleLogout"
              class="text-sm text-gray-500 hover:text-gray-700"
            >
              登出
            </button>
          </div>
        </div>
      </div>
    </nav>

    <!-- 主要内容 -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- 用户信息卡片 -->
      <div class="bg-white rounded-lg shadow p-6 mb-8">
        <h2 class="text-lg font-medium text-gray-900 mb-4">用户信息</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <dt class="text-sm font-medium text-gray-500">用户名</dt>
            <dd class="mt-1 text-sm text-gray-900">{{ user?.name }}</dd>
          </div>
          <div>
            <dt class="text-sm font-medium text-gray-500">邮箱</dt>
            <dd class="mt-1 text-sm text-gray-900">{{ user?.email || '未设置' }}</dd>
          </div>
          <div>
            <dt class="text-sm font-medium text-gray-500">注册时间</dt>
            <dd class="mt-1 text-sm text-gray-900">{{ formatDate(user?.createdAt) }}</dd>
          </div>
        </div>
      </div>

      <!-- API密钥管理 -->
      <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
          <div class="flex items-center justify-between">
            <h2 class="text-lg font-medium text-gray-900">我的API密钥</h2>
            <button
              @click="showCreateModal = true"
              class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
            >
              <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              创建API密钥
            </button>
          </div>
        </div>

        <div class="p-6">
          <!-- 统计信息 -->
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div class="text-center p-4 bg-green-50 rounded-lg">
              <div class="text-2xl font-bold text-green-600">{{ stats.total }}</div>
              <div class="text-sm text-gray-600">总密钥数</div>
            </div>
            <div class="text-center p-4 bg-blue-50 rounded-lg">
              <div class="text-2xl font-bold text-blue-600">{{ stats.active }}</div>
              <div class="text-sm text-gray-600">启用中</div>
            </div>
            <div class="text-center p-4 bg-gray-50 rounded-lg">
              <div class="text-2xl font-bold text-gray-600">{{ stats.disabled }}</div>
              <div class="text-sm text-gray-600">已禁用</div>
            </div>
          </div>

          <!-- API密钥列表 -->
          <div v-if="loading" class="text-center py-8">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
            <p class="mt-2 text-sm text-gray-500">加载中...</p>
          </div>

          <div v-else-if="apiKeys.length === 0" class="text-center py-8">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v-2H7v-2H4a1 1 0 01-1-1v-4c0-2.632 2.122-5.367 5.5-5.5C9.5 2.5 12.5 2.5 15 7z" />
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">暂无API密钥</h3>
            <p class="mt-1 text-sm text-gray-500">创建您的第一个API密钥来开始使用</p>
          </div>

          <div v-else class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
            <table class="min-w-full divide-y divide-gray-300">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">名称</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">密钥</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建时间</th>
                  <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="apiKey in apiKeys" :key="apiKey.id" class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm font-medium text-gray-900">{{ apiKey.name }}</div>
                    <div v-if="apiKey.description" class="text-sm text-gray-500">{{ apiKey.description }}</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center space-x-2">
                      <code class="text-sm bg-gray-100 px-2 py-1 rounded">{{ maskApiKey(apiKey.key) }}</code>
                      <button
                        @click="copyApiKey(apiKey.key)"
                        class="text-gray-400 hover:text-gray-600"
                        title="复制密钥"
                      >
                        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                        </svg>
                      </button>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span :class="{
                      'inline-flex px-2 py-1 text-xs font-semibold rounded-full': true,
                      'bg-green-100 text-green-800': apiKey.status === 'Active',
                      'bg-red-100 text-red-800': apiKey.status === 'Disabled'
                    }">
                      {{ apiKey.status === 'Active' ? '启用' : '禁用' }}
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {{ formatDate(apiKey.createdAt) }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div class="flex items-center justify-end space-x-2">
                      <button
                        @click="toggleApiKeyStatus(apiKey)"
                        :class="{
                          'text-red-600 hover:text-red-900': apiKey.status === 'Active',
                          'text-green-600 hover:text-green-900': apiKey.status === 'Disabled'
                        }"
                      >
                        {{ apiKey.status === 'Active' ? '禁用' : '启用' }}
                      </button>
                      <button
                        @click="deleteApiKey(apiKey)"
                        class="text-red-600 hover:text-red-900"
                      >
                        删除
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- 创建API密钥模态框 -->
    <div v-if="showCreateModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 w-full max-w-md">
        <h3 class="text-lg font-medium mb-4">创建API密钥</h3>
        <form @submit.prevent="handleCreateApiKey">
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">密钥名称</label>
              <input
                v-model="form.name"
                type="text"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                placeholder="请输入密钥名称"
              >
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">描述</label>
              <textarea
                v-model="form.description"
                rows="3"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                placeholder="请输入描述信息（可选）"
              ></textarea>
            </div>
          </div>
          <div class="flex justify-end space-x-3 mt-6">
            <button
              type="button"
              @click="closeCreateModal"
              class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
            >
              取消
            </button>
            <button
              type="submit"
              :disabled="!form.name.trim() || creating"
              class="px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-green-600 to-blue-600 rounded-md hover:from-green-700 hover:to-blue-700 disabled:opacity-50"
            >
              {{ creating ? '创建中...' : '创建' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup>
// 页面元数据
definePageMeta({
  title: '用户仪表板 - EasyPool',
  middleware: 'user-auth'
})

// 设置页面标题
useHead({
  title: '用户仪表板 - EasyPool'
})

// 响应式数据
const user = ref(null)
const apiKeys = ref([])
const loading = ref(true)
const creating = ref(false)
const showCreateModal = ref(false)

const form = ref({
  name: '',
  description: ''
})

// 计算属性
const stats = computed(() => {
  if (!apiKeys.value || apiKeys.value.length === 0) {
    return { total: 0, active: 0, disabled: 0 }
  }
  
  const stats = {
    total: apiKeys.value.length,
    active: 0,
    disabled: 0
  }
  
  apiKeys.value.forEach(apiKey => {
    if (apiKey.status === 'Active') {
      stats.active++
    } else {
      stats.disabled++
    }
  })
  
  return stats
})

// 方法
const loadData = async () => {
  try {
    loading.value = true
    
    // 获取用户信息
    const userResponse = await $fetch('/api/user/auth/me')
    if (userResponse.success) {
      user.value = userResponse.user
    }
    
    // 获取API密钥列表
    const apiKeysResponse = await $fetch('/api/user/api-keys')
    if (apiKeysResponse.success) {
      apiKeys.value = apiKeysResponse.data
    }
  } catch (error) {
    console.error('Load data error:', error)
    if (error.statusCode === 401) {
      await navigateTo('/user/login')
    }
  } finally {
    loading.value = false
  }
}

const handleLogout = async () => {
  try {
    await $fetch('/api/user/auth/logout', { method: 'POST' })
    await navigateTo('/user/login')
  } catch (error) {
    console.error('Logout error:', error)
    // 即使出错也跳转到登录页
    await navigateTo('/user/login')
  }
}

const handleCreateApiKey = async () => {
  if (creating.value) return
  
  try {
    creating.value = true
    
    const response = await $fetch('/api/user/api-keys', {
      method: 'POST',
      body: {
        name: form.value.name.trim(),
        description: form.value.description.trim() || null
      }
    })
    
    if (response.success) {
      alert(`API密钥创建成功！\n\n密钥: ${response.data.key}\n\n请妥善保存，密钥只显示一次！`)
      closeCreateModal()
      await loadData()
    } else {
      alert(response.message || '创建失败')
    }
  } catch (error) {
    console.error('Create API key error:', error)
    alert('创建失败，请重试')
  } finally {
    creating.value = false
  }
}

const closeCreateModal = () => {
  showCreateModal.value = false
  form.value = {
    name: '',
    description: ''
  }
}

const toggleApiKeyStatus = async (apiKey) => {
  const newStatus = apiKey.status === 'Active' ? 'Disabled' : 'Active'
  const action = newStatus === 'Active' ? '启用' : '禁用'
  
  if (!confirm(`确定要${action}密钥 "${apiKey.name}" 吗？`)) return
  
  try {
    const response = await $fetch(`/api/user/api-keys/${apiKey.id}/status`, {
      method: 'PUT',
      body: { status: newStatus }
    })
    
    if (response.success) {
      await loadData()
    } else {
      alert(response.message || `${action}失败`)
    }
  } catch (error) {
    console.error('Toggle API key status error:', error)
    alert(`${action}失败，请重试`)
  }
}

const deleteApiKey = async (apiKey) => {
  if (!confirm(`确定要删除密钥 "${apiKey.name}" 吗？此操作不可恢复！`)) return
  
  try {
    const response = await $fetch(`/api/user/api-keys/${apiKey.id}`, {
      method: 'DELETE'
    })
    
    if (response.success) {
      await loadData()
    } else {
      alert(response.message || '删除失败')
    }
  } catch (error) {
    console.error('Delete API key error:', error)
    alert('删除失败，请重试')
  }
}

const copyApiKey = async (key) => {
  try {
    await navigator.clipboard.writeText(key)
    alert('API密钥已复制到剪贴板')
  } catch (error) {
    console.error('Copy error:', error)
    alert('复制失败，请手动复制')
  }
}

const maskApiKey = (key) => {
  if (!key) return ''
  return key.substring(0, 8) + '...' + key.substring(key.length - 8)
}

const formatDate = (dateString) => {
  if (!dateString) return '未知'
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN')
}

// 页面加载时获取数据
onMounted(() => {
  loadData()
})
</script>
