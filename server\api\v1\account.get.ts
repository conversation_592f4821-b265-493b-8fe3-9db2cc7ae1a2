/**
 * 获取账号接口（取号）
 * GET /api/v1/account
 * 
 * 这是系统的核心业务API，使用事务确保操作的原子性，防止并发冲突
 */

import { prisma, executeTransaction } from '../../utils/db'
import { requireApiKey, extractApiKey } from '../../utils/auth'

export default defineEventHandler(async (event) => {
  try {
    // 1. API密钥认证
    await requireApiKey(event)

    // 获取API密钥字符串用作用户标识
    const apiKeyString = extractApiKey(event)

    // 2. 获取查询参数
    const query = getQuery(event)
    const { poolName, poolType, userId } = query

    // 使用API密钥作为用户标识（如果没有提供userId）
    const userIdentifier = (userId as string) || apiKeyString

    // 3. 参数验证
    if (!poolName && !poolType) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Either poolName or poolType is required'
      })
    }

    // 4. 使用事务确保取号操作的原子性
    const result = await executeTransaction(async (tx) => {
      let targetPool = null

      // 根据参数查找目标号池
      if (poolName) {
        // 按号池名称查找
        targetPool = await tx.pool.findUnique({
          where: { name: poolName as string },
          include: {
            poolType: true
          }
        })

        if (!targetPool) {
          throw createError({
            statusCode: 404,
            statusMessage: `Pool '${poolName}' not found`
          })
        }
      } else if (poolType) {
        // 按号池类型查找，选择第一个可用的号池
        const poolTypeRecord = await tx.poolType.findFirst({
          where: { name: poolType as string },
          include: {
            pools: {
              include: {
                _count: {
                  select: {
                    accounts: {
                      where: {
                        status: 'Available',
                        OR: [
                          { expiresAt: null },
                          { expiresAt: { gt: new Date() } }
                        ]
                      }
                    }
                  }
                }
              }
            }
          }
        })

        if (!poolTypeRecord || poolTypeRecord.pools.length === 0) {
          throw createError({
            statusCode: 404,
            statusMessage: `No pools found for pool type '${poolType}'`
          })
        }

        // 选择有可用账号的号池
        const availablePool = poolTypeRecord.pools.find(pool => pool._count.accounts > 0)
        if (!availablePool) {
          throw createError({
            statusCode: 404,
            statusMessage: `No available accounts in pool type '${poolType}'`
          })
        }

        targetPool = {
          id: availablePool.id,
          name: availablePool.name,
          poolType: {
            id: poolTypeRecord.id,
            name: poolTypeRecord.name
          }
        }
      }

      // 账号占用跟踪逻辑：检查用户是否已占用同类型的账号
      if (userIdentifier) {
        // 查找用户已占用的同类型账号
        const occupiedAccounts = await tx.account.findMany({
          where: {
            occupiedBy: userIdentifier,
            pool: {
              poolTypeId: targetPool.poolType.id
            },
            status: 'InUse'
          },
          include: {
            pool: {
              include: {
                poolType: true
              }
            }
          }
        })

        // 如果用户已占用同类型账号，先释放这些账号
        if (occupiedAccounts.length > 0) {
          await tx.account.updateMany({
            where: {
              id: {
                in: occupiedAccounts.map(acc => acc.id)
              }
            },
            data: {
              status: 'Available',
              occupiedBy: null,
              lastUsedAt: new Date()
            }
          })

          console.log(`Released ${occupiedAccounts.length} previously occupied accounts for user: ${userIdentifier}`)
        }
      }

      // 查找可用账号（先进先出，优先使用最早创建的账号）
      const availableAccount = await tx.account.findFirst({
        where: {
          poolId: targetPool.id,
          status: 'Available',
          OR: [
            { expiresAt: null },
            { expiresAt: { gt: new Date() } }
          ]
        },
        orderBy: {
          createdAt: 'asc' // 先进先出
        }
      })

      if (!availableAccount) {
        throw createError({
          statusCode: 404,
          statusMessage: `No available accounts in pool '${targetPool.name}'`
        })
      }

      // 更新账号状态为占用中，并设置占用标识
      const updatedAccount = await tx.account.update({
        where: { id: availableAccount.id },
        data: {
          status: 'InUse',
          occupiedBy: userIdentifier,
          lastUsedAt: new Date()
        }
      })

      return {
        account: updatedAccount,
        pool: targetPool
      }
    })

    // 5. 记录取号日志
    console.log(`Account retrieved: ${result.account.id} from pool: ${result.pool.name} by user: ${userIdentifier}`)

    // 6. 返回账号信息
    return {
      success: true,
      data: {
        accountId: result.account.id,
        content: result.account.content,
        poolName: result.pool.name,
        poolType: result.pool.poolType.name,
        expiresAt: result.account.expiresAt,
        retrievedAt: result.account.lastUsedAt,
        notes: result.account.notes
      }
    }
  } catch (error) {
    console.error('Failed to retrieve account:', error)
    
    // 如果是已知的错误（如验证错误），直接抛出
    if (error.statusCode) {
      throw error
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to retrieve account'
    })
  }
})
