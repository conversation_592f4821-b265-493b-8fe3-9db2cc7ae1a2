<template>
  <UiCard :variant="variant" :hoverable="clickable" @click="handleClick">
    <div class="flex items-center">
      <div class="flex-1">
        <div class="flex items-center">
          <div
            v-if="icon"
            :class="iconClasses"
            class="flex items-center justify-center w-12 h-12 rounded-lg"
          >
            <component :is="icon" class="h-6 w-6" />
          </div>
          
          <div :class="icon ? 'ml-4' : ''">
            <p class="text-sm font-medium text-gray-600">{{ title }}</p>
            <div class="flex items-baseline">
              <p :class="valueClasses" class="text-2xl font-semibold">
                {{ formattedValue }}
              </p>
              <p v-if="unit" class="ml-1 text-sm text-gray-500">{{ unit }}</p>
            </div>
          </div>
        </div>
        
        <!-- 变化趋势 -->
        <div v-if="change !== undefined" class="mt-2 flex items-center">
          <div :class="changeClasses" class="flex items-center text-sm font-medium">
            <component :is="changeIcon" class="h-4 w-4 mr-1" />
            {{ Math.abs(change) }}{{ changeUnit }}
          </div>
          <span class="ml-2 text-sm text-gray-500">{{ changeLabel }}</span>
        </div>
        
        <!-- 描述信息 -->
        <p v-if="description" class="mt-1 text-sm text-gray-500">
          {{ description }}
        </p>
      </div>
      
      <!-- 右侧内容 -->
      <div v-if="$slots.actions" class="ml-4">
        <slot name="actions" />
      </div>
    </div>
    
    <!-- 进度条 -->
    <div v-if="progress !== undefined" class="mt-4">
      <div class="flex justify-between text-sm text-gray-600 mb-1">
        <span>{{ progressLabel || '进度' }}</span>
        <span>{{ progress }}%</span>
      </div>
      <div class="w-full bg-gray-200 rounded-full h-2">
        <div
          :class="progressBarClasses"
          class="h-2 rounded-full transition-all duration-300"
          :style="{ width: `${Math.min(progress, 100)}%` }"
        ></div>
      </div>
    </div>
  </UiCard>
</template>

<script setup lang="ts">
interface Props {
  title: string
  value: number | string
  unit?: string
  description?: string
  icon?: any
  iconColor?: 'blue' | 'green' | 'yellow' | 'red' | 'purple' | 'gray'
  variant?: 'default' | 'outlined' | 'elevated'
  change?: number
  changeUnit?: string
  changeLabel?: string
  progress?: number
  progressLabel?: string
  progressColor?: 'blue' | 'green' | 'yellow' | 'red'
  clickable?: boolean
  loading?: boolean
}

interface Emits {
  click: []
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'default',
  iconColor: 'blue',
  changeUnit: '%',
  changeLabel: '与上期相比',
  progressColor: 'blue',
  clickable: false,
  loading: false
})

const emit = defineEmits<Emits>()

// 图标组件
const TrendingUpIcon = h('svg', {
  fill: 'none',
  stroke: 'currentColor',
  viewBox: '0 0 24 24'
}, h('path', {
  'stroke-linecap': 'round',
  'stroke-linejoin': 'round',
  'stroke-width': '2',
  d: 'M13 7h8m0 0v8m0-8l-8 8-4-4-6 6'
}))

const TrendingDownIcon = h('svg', {
  fill: 'none',
  stroke: 'currentColor',
  viewBox: '0 0 24 24'
}, h('path', {
  'stroke-linecap': 'round',
  'stroke-linejoin': 'round',
  'stroke-width': '2',
  d: 'M13 17h8m0 0V9m0 8l-8-8-4 4-6-6'
}))

// 计算属性
const formattedValue = computed(() => {
  if (props.loading) return '--'
  
  if (typeof props.value === 'number') {
    return formatNumber(props.value)
  }
  return props.value
})

const iconClasses = computed(() => {
  const colorClasses = {
    blue: 'bg-blue-100 text-blue-600',
    green: 'bg-green-100 text-green-600',
    yellow: 'bg-yellow-100 text-yellow-600',
    red: 'bg-red-100 text-red-600',
    purple: 'bg-purple-100 text-purple-600',
    gray: 'bg-gray-100 text-gray-600'
  }
  
  return colorClasses[props.iconColor]
})

const valueClasses = computed(() => {
  if (props.loading) return 'text-gray-400'
  return 'text-gray-900'
})

const changeClasses = computed(() => {
  if (props.change === undefined) return ''
  
  if (props.change > 0) {
    return 'text-green-600'
  } else if (props.change < 0) {
    return 'text-red-600'
  } else {
    return 'text-gray-600'
  }
})

const changeIcon = computed(() => {
  if (props.change === undefined) return null
  
  if (props.change > 0) {
    return TrendingUpIcon
  } else if (props.change < 0) {
    return TrendingDownIcon
  } else {
    return null
  }
})

const progressBarClasses = computed(() => {
  const colorClasses = {
    blue: 'bg-blue-500',
    green: 'bg-green-500',
    yellow: 'bg-yellow-500',
    red: 'bg-red-500'
  }
  
  return colorClasses[props.progressColor]
})

// 方法
const handleClick = () => {
  if (props.clickable && !props.loading) {
    emit('click')
  }
}
</script>
