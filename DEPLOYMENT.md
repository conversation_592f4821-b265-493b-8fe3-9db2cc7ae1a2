# 部署指南

本文档详细介绍了简易号池管理系统的部署方法和配置说明。

## 目录

- [环境要求](#环境要求)
- [本地开发部署](#本地开发部署)
- [生产环境部署](#生产环境部署)
- [数据库配置](#数据库配置)
- [环境变量配置](#环境变量配置)
- [部署验证](#部署验证)
- [常见问题](#常见问题)

## 环境要求

### 基础要求
- **Node.js**: 18.0.0 或更高版本
- **npm**: 9.0.0 或更高版本（或 yarn/pnpm）
- **数据库**: MySQL 8.0+ 或 TiDB Cloud

### 推荐配置
- **内存**: 最少 2GB RAM
- **存储**: 最少 10GB 可用空间
- **网络**: 稳定的互联网连接

## 本地开发部署

### 1. 克隆项目
```bash
git clone <repository-url>
cd qnb-pool
```

### 2. 安装依赖
```bash
npm install
# 或
yarn install
# 或
pnpm install
```

### 3. 环境变量配置
复制环境变量模板：
```bash
cp .env.example .env
```

编辑 `.env` 文件：
```env
# 数据库连接
DATABASE_URL="mysql://username:password@host:port/database"

# 应用配置
NUXT_SECRET_KEY="your-secret-key-here"
NUXT_PUBLIC_APP_URL="http://localhost:3000"

# 开发环境配置
NODE_ENV="development"
```

### 4. 数据库初始化
```bash
# 生成 Prisma Client
npx prisma generate

# 推送数据库模式
npx prisma db push

# （可选）填充示例数据
npx prisma db seed
```

### 5. 启动开发服务器
```bash
npm run dev
```

访问 `http://localhost:3000` 查看应用。

## 生产环境部署

### Vercel 部署（推荐）

#### 1. 准备工作
- 确保代码已推送到 GitHub/GitLab/Bitbucket
- 注册 [Vercel](https://vercel.com) 账号

#### 2. 部署步骤
1. 在 Vercel 控制台点击 "New Project"
2. 导入您的 Git 仓库
3. 配置环境变量（见下方配置说明）
4. 点击 "Deploy"

#### 3. 环境变量配置
在 Vercel 项目设置中添加以下环境变量：
```
DATABASE_URL=mysql://username:password@host:port/database
NUXT_SECRET_KEY=your-production-secret-key
NUXT_PUBLIC_APP_URL=https://your-domain.vercel.app
NODE_ENV=production
```

### Netlify 部署

#### 1. 构建配置
创建 `netlify.toml` 文件：
```toml
[build]
  command = "npm run build"
  publish = ".output/public"

[build.environment]
  NODE_VERSION = "18"

[[redirects]]
  from = "/*"
  to = "/.netlify/functions/server"
  status = 200
```

#### 2. 部署步骤
1. 连接 Git 仓库到 Netlify
2. 配置构建命令：`npm run build`
3. 配置发布目录：`.output/public`
4. 添加环境变量
5. 部署

### Docker 部署

#### 1. 创建 Dockerfile
```dockerfile
FROM node:18-alpine

WORKDIR /app

# 复制 package 文件
COPY package*.json ./
COPY prisma ./prisma/

# 安装依赖
RUN npm ci --only=production

# 复制源代码
COPY . .

# 生成 Prisma Client
RUN npx prisma generate

# 构建应用
RUN npm run build

EXPOSE 3000

CMD ["npm", "start"]
```

#### 2. 构建和运行
```bash
# 构建镜像
docker build -t qnb-pool .

# 运行容器
docker run -p 3000:3000 \
  -e DATABASE_URL="your-database-url" \
  -e NUXT_SECRET_KEY="your-secret-key" \
  qnb-pool
```

### VPS/服务器部署

#### 1. 服务器准备
```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装 Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 安装 PM2
sudo npm install -g pm2
```

#### 2. 部署应用
```bash
# 克隆代码
git clone <repository-url>
cd qnb-pool

# 安装依赖
npm ci --production

# 配置环境变量
cp .env.example .env
# 编辑 .env 文件

# 生成 Prisma Client
npx prisma generate

# 构建应用
npm run build

# 使用 PM2 启动
pm2 start ecosystem.config.js
```

#### 3. PM2 配置
创建 `ecosystem.config.js`：
```javascript
module.exports = {
  apps: [{
    name: 'qnb-pool',
    script: '.output/server/index.mjs',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    }
  }]
}
```

## 数据库配置

### TiDB Cloud（推荐）

#### 1. 创建集群
1. 访问 [TiDB Cloud](https://tidbcloud.com)
2. 创建免费的 Serverless 集群
3. 获取连接字符串

#### 2. 配置连接
```env
DATABASE_URL="mysql://username:<EMAIL>:4000/database?sslaccept=strict"
```

### MySQL 8.0+

#### 1. 安装 MySQL
```bash
# Ubuntu/Debian
sudo apt install mysql-server

# CentOS/RHEL
sudo yum install mysql-server
```

#### 2. 创建数据库和用户
```sql
CREATE DATABASE qnb_pool;
CREATE USER 'qnb_user'@'%' IDENTIFIED BY 'strong_password';
GRANT ALL PRIVILEGES ON qnb_pool.* TO 'qnb_user'@'%';
FLUSH PRIVILEGES;
```

#### 3. 配置连接
```env
DATABASE_URL="mysql://qnb_user:strong_password@localhost:3306/qnb_pool"
```

## 环境变量配置

### 必需变量
```env
# 数据库连接（必需）
DATABASE_URL="mysql://username:password@host:port/database"

# 应用密钥（必需）
NUXT_SECRET_KEY="your-secret-key-minimum-32-characters"

# 应用URL（必需）
NUXT_PUBLIC_APP_URL="https://your-domain.com"
```

### 可选变量
```env
# 环境标识
NODE_ENV="production"

# 端口配置
PORT=3000

# 日志级别
LOG_LEVEL="info"

# API限流配置
RATE_LIMIT_MAX=100
RATE_LIMIT_WINDOW=900000

# 会话配置
SESSION_TIMEOUT=3600000
```

### 安全建议
- 使用强密码和复杂的密钥
- 定期轮换 API 密钥
- 启用数据库 SSL 连接
- 配置防火墙规则
- 使用环境变量管理敏感信息

## 部署验证

### 1. 健康检查
访问以下端点验证部署：
```bash
# 应用健康检查
curl https://your-domain.com/api/v1/health

# 预期响应
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "version": "1.0.0"
}
```

### 2. 功能测试
1. 访问主页面，确认界面正常显示
2. 生成 API 密钥
3. 创建号池类型和号池
4. 添加测试账号
5. 测试取号和归还功能

### 3. 性能测试
```bash
# 使用 ab 进行简单压力测试
ab -n 1000 -c 10 https://your-domain.com/

# 使用 wrk 进行更详细的测试
wrk -t12 -c400 -d30s https://your-domain.com/
```

## 常见问题

### Q: 数据库连接失败
**A:** 检查以下项目：
- 数据库服务是否运行
- 连接字符串是否正确
- 网络连接是否正常
- 防火墙设置是否允许连接

### Q: 构建失败
**A:** 常见解决方案：
```bash
# 清理缓存
rm -rf node_modules .nuxt .output
npm install

# 重新生成 Prisma Client
npx prisma generate

# 重新构建
npm run build
```

### Q: 内存不足
**A:** 优化建议：
- 增加服务器内存
- 配置 Node.js 内存限制：`--max-old-space-size=2048`
- 使用 PM2 集群模式

### Q: SSL 证书问题
**A:** 解决方案：
- 使用 Let's Encrypt 免费证书
- 配置 Nginx 反向代理
- 使用 Cloudflare 等 CDN 服务

### Q: 性能优化
**A:** 建议措施：
- 启用 Gzip 压缩
- 配置 CDN
- 使用数据库连接池
- 启用缓存机制

## 监控和维护

### 1. 日志监控
```bash
# PM2 日志查看
pm2 logs qnb-pool

# 实时日志监控
pm2 logs qnb-pool --lines 100 -f
```

### 2. 性能监控
- 使用 PM2 监控面板
- 配置 New Relic 或 DataDog
- 设置告警规则

### 3. 备份策略
```bash
# 数据库备份
mysqldump -u username -p database_name > backup.sql

# 定期备份脚本
0 2 * * * /path/to/backup-script.sh
```

### 4. 更新部署
```bash
# 拉取最新代码
git pull origin main

# 安装新依赖
npm install

# 重新构建
npm run build

# 重启应用
pm2 restart qnb-pool
```

---

如需更多帮助，请查看 [使用文档](./USAGE.md) 或提交 Issue。
