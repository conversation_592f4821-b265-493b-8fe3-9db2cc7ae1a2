<template>
  <div class="bg-white shadow-sm border-b">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex items-center justify-between h-16">
        <!-- 左侧：Logo和面包屑导航 -->
        <div class="flex items-center space-x-4">
          <!-- Logo -->
          <NuxtLink to="/" class="flex items-center">
            <h1 class="text-2xl font-bold text-gray-900">EasyPool</h1>
            <span class="ml-2 text-sm text-gray-500">简易号池管理系统</span>
          </NuxtLink>

          <!-- 面包屑导航 -->
          <nav v-if="breadcrumbs && breadcrumbs.length > 0" class="flex items-center space-x-2 ml-6">
            <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
            <ol class="flex items-center space-x-2">
              <li v-for="(breadcrumb, index) in breadcrumbs" :key="index" class="flex items-center">
                <template v-if="index > 0">
                  <svg class="w-4 h-4 text-gray-400 mx-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                  </svg>
                </template>
                <NuxtLink
                  v-if="breadcrumb.to && index < breadcrumbs.length - 1"
                  :to="breadcrumb.to"
                  class="text-sm font-medium text-gray-500 hover:text-gray-700"
                >
                  {{ breadcrumb.text }}
                </NuxtLink>
                <span
                  v-else
                  class="text-sm font-medium"
                  :class="index === breadcrumbs.length - 1 ? 'text-gray-900' : 'text-gray-500'"
                >
                  {{ breadcrumb.text }}
                </span>
              </li>
            </ol>
          </nav>
        </div>

        <!-- 右侧：操作按钮 -->
        <div class="flex items-center space-x-3">
          <!-- 自定义操作按钮插槽 -->
          <slot name="actions" />

          <!-- 默认操作按钮 -->
          <template v-if="!$slots.actions">
            <!-- API密钥管理按钮 -->
            <NuxtLink to="/api-keys">
              <button
                class="flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
              >
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
                </svg>
                API密钥
              </button>
            </NuxtLink>

            <!-- 刷新按钮 -->
            <button
              v-if="showRefresh"
              @click="$emit('refresh')"
              :disabled="refreshing"
              class="flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50"
            >
              <svg 
                class="w-4 h-4" 
                :class="{ 'animate-spin': refreshing }"
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
            </button>

            <!-- 设置按钮 -->
            <button
              v-if="showSettings"
              @click="$emit('settings')"
              class="flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
            </button>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// 定义组件属性
const props = defineProps({
  // 面包屑导航数据
  breadcrumbs: {
    type: Array,
    default: () => []
  },
  // 是否显示刷新按钮
  showRefresh: {
    type: Boolean,
    default: false
  },
  // 刷新状态
  refreshing: {
    type: Boolean,
    default: false
  },
  // 是否显示设置按钮
  showSettings: {
    type: Boolean,
    default: false
  }
})

// 定义事件
const emit = defineEmits(['refresh', 'settings'])
</script>
