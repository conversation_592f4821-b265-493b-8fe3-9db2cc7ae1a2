/**
 * 创建API密钥接口
 * POST /api/admin/api-keys
 */

import { prisma } from '../../../utils/db'

export default defineEventHandler(async (event) => {
  try {
    // 获取请求体
    const body = await readBody(event)
    const { name, description, expiresAt, userId, permissions } = body

    // 验证必填字段
    if (!name || typeof name !== 'string' || name.trim().length === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: 'API key name is required'
      })
    }

    // 验证名称长度
    if (name.trim().length > 100) {
      throw createError({
        statusCode: 400,
        statusMessage: 'API key name must be 100 characters or less'
      })
    }

    // 验证描述长度
    if (description && typeof description === 'string' && description.length > 500) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Description must be 500 characters or less'
      })
    }

    // 处理过期时间
    let expiresAtDate = null
    if (expiresAt) {
      expiresAtDate = new Date(expiresAt)
      if (isNaN(expiresAtDate.getTime())) {
        throw createError({
          statusCode: 400,
          statusMessage: 'Invalid expires date format'
        })
      }
      
      // 检查过期时间是否在未来
      if (expiresAtDate <= new Date()) {
        throw createError({
          statusCode: 400,
          statusMessage: 'Expiration date must be in the future'
        })
      }
    }

    // 检查名称是否已存在
    const existingApiKey = await prisma.apiKey.findFirst({
      where: {
        name: name.trim()
      }
    })

    if (existingApiKey) {
      throw createError({
        statusCode: 409,
        statusMessage: 'API key with this name already exists'
      })
    }

    // 验证用户ID（如果提供）
    let validUserId = null
    if (userId && typeof userId === 'string') {
      const user = await prisma.user.findUnique({
        where: { id: userId }
      })

      if (!user) {
        throw createError({
          statusCode: 404,
          statusMessage: 'User not found'
        })
      }

      validUserId = userId
    }

    // 验证权限配置（如果提供）
    let permissionsJson = null
    if (permissions && Array.isArray(permissions)) {
      // 这里可以添加权限验证逻辑
      permissionsJson = JSON.stringify(permissions)
    }

    // 创建API密钥
    const newApiKey = await prisma.apiKey.create({
      data: {
        name: name.trim(),
        description: description ? description.trim() : null,
        expiresAt: expiresAtDate,
        status: 'Active',
        userId: validUserId,
        permissions: permissionsJson
      },
      include: {
        user: true
      }
    })

    return {
      success: true,
      data: newApiKey
    }
  } catch (error) {
    console.error('Failed to create API key:', error)
    
    // 如果是已知的错误（如验证错误），直接抛出
    if (error.statusCode) {
      throw error
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to create API key'
    })
  }
})
