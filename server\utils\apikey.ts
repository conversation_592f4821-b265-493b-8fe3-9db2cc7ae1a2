/**
 * API密钥管理工具
 * 提供API密钥的创建、验证、管理等功能
 */

import { prisma, executeTransaction } from './db'

/**
 * 获取当前有效的API密钥
 */
export async function getCurrentApiKey() {
  try {
    const apiKey = await prisma.apiKey.findFirst({
      orderBy: {
        createdAt: 'desc'
      }
    })
    
    return apiKey
  } catch (error) {
    console.error('Failed to get current API key:', error)
    throw error
  }
}

/**
 * 创建新的API密钥（如果不存在）
 */
export async function ensureApiKeyExists(): Promise<string> {
  try {
    const existingKey = await getCurrentApiKey()
    
    if (existingKey) {
      return existingKey.key
    }
    
    // 创建新的API密钥
    const newKey = await prisma.apiKey.create({
      data: {}
    })
    
    console.log(`New API key created: ${newKey.key.substring(0, 8)}...`)
    return newKey.key
  } catch (error) {
    console.error('Failed to ensure API key exists:', error)
    throw error
  }
}

/**
 * 重新生成API密钥
 * 删除所有现有密钥并创建新的密钥
 */
export async function regenerateApiKey(): Promise<{
  id: number
  key: string
  createdAt: Date
}> {
  try {
    return await executeTransaction(async (tx) => {
      // 删除所有现有的API密钥
      await tx.apiKey.deleteMany({})
      
      // 创建新的API密钥
      const newApiKey = await tx.apiKey.create({
        data: {}
      })
      
      console.log(`API key regenerated: ${newApiKey.key.substring(0, 8)}...`)
      return newApiKey
    })
  } catch (error) {
    console.error('Failed to regenerate API key:', error)
    throw error
  }
}

/**
 * 验证API密钥格式
 * 检查密钥是否符合预期格式
 */
export function validateApiKeyFormat(key: string): boolean {
  // API密钥应该是cuid格式，长度通常为25个字符
  const cuidRegex = /^[a-z0-9]{25}$/
  return cuidRegex.test(key)
}

/**
 * 脱敏显示API密钥
 * 只显示前8位和后4位，中间用...代替
 */
export function maskApiKey(key: string): string {
  if (key.length < 12) {
    return key.substring(0, 4) + '...'
  }
  
  return `${key.substring(0, 8)}...${key.substring(key.length - 4)}`
}

/**
 * 获取API密钥统计信息
 */
export async function getApiKeyStats() {
  try {
    const totalKeys = await prisma.apiKey.count()
    const currentKey = await getCurrentApiKey()
    
    return {
      totalKeys,
      hasCurrentKey: !!currentKey,
      currentKeyCreatedAt: currentKey?.createdAt,
      currentKeyMasked: currentKey ? maskApiKey(currentKey.key) : null
    }
  } catch (error) {
    console.error('Failed to get API key stats:', error)
    throw error
  }
}
