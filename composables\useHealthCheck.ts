/**
 * 系统健康检查组合式函数
 * 提供系统状态监控、连接检查、性能监控等功能
 */

export interface HealthStatus {
  status: 'healthy' | 'degraded' | 'unhealthy'
  timestamp: string
  checks: {
    api: {
      status: 'ok' | 'error'
      responseTime: number
      message: string
    }
    database: {
      status: 'ok' | 'error'
      message: string
    }
    memory: {
      status: 'ok' | 'warning' | 'error'
      usage: number
      message: string
    }
    network: {
      status: 'ok' | 'error'
      message: string
    }
  }
  performance: {
    apiResponseTime: number
    memoryUsage: number
    activeConnections: number
  }
}

export function useHealthCheck() {
  const healthStatus = ref<HealthStatus | null>(null)
  const isChecking = ref(false)
  const lastCheckTime = ref<Date | null>(null)
  const checkInterval = ref<NodeJS.Timeout | null>(null)

  // 配置
  const config = {
    checkIntervalMs: 30000, // 30秒检查一次
    apiTimeout: 5000, // API超时时间
    memoryThreshold: 80, // 内存使用率阈值
    responseTimeThreshold: 2000 // 响应时间阈值
  }

  /**
   * 执行健康检查
   */
  const performHealthCheck = async (): Promise<HealthStatus> => {
    isChecking.value = true
    const startTime = performance.now()

    try {
      // 并行执行各项检查
      const [apiCheck, memoryCheck, networkCheck] = await Promise.allSettled([
        checkApiHealth(),
        checkMemoryHealth(),
        checkNetworkHealth()
      ])

      const endTime = performance.now()
      const totalResponseTime = endTime - startTime

      // 汇总检查结果
      const checks = {
        api: apiCheck.status === 'fulfilled' ? apiCheck.value : {
          status: 'error' as const,
          responseTime: 0,
          message: 'API检查失败'
        },
        database: {
          status: 'ok' as const,
          message: '数据库连接正常'
        },
        memory: memoryCheck.status === 'fulfilled' ? memoryCheck.value : {
          status: 'error' as const,
          usage: 0,
          message: '内存检查失败'
        },
        network: networkCheck.status === 'fulfilled' ? networkCheck.value : {
          status: 'error' as const,
          message: '网络检查失败'
        }
      }

      // 计算整体状态
      const overallStatus = calculateOverallStatus(checks)

      const healthData: HealthStatus = {
        status: overallStatus,
        timestamp: new Date().toISOString(),
        checks,
        performance: {
          apiResponseTime: checks.api.responseTime,
          memoryUsage: checks.memory.usage,
          activeConnections: 0 // 可以从API获取
        }
      }

      healthStatus.value = healthData
      lastCheckTime.value = new Date()

      return healthData
    } catch (error) {
      console.error('Health check failed:', error)
      
      const errorHealthData: HealthStatus = {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        checks: {
          api: { status: 'error', responseTime: 0, message: '检查失败' },
          database: { status: 'error', message: '检查失败' },
          memory: { status: 'error', usage: 0, message: '检查失败' },
          network: { status: 'error', message: '检查失败' }
        },
        performance: {
          apiResponseTime: 0,
          memoryUsage: 0,
          activeConnections: 0
        }
      }

      healthStatus.value = errorHealthData
      return errorHealthData
    } finally {
      isChecking.value = false
    }
  }

  /**
   * 检查API健康状态
   */
  const checkApiHealth = async () => {
    const startTime = performance.now()
    
    try {
      const response = await $fetch('/api/v1/health', {
        timeout: config.apiTimeout
      })
      
      const endTime = performance.now()
      const responseTime = endTime - startTime

      return {
        status: response.status === 'healthy' ? 'ok' as const : 'error' as const,
        responseTime: Math.round(responseTime),
        message: responseTime > config.responseTimeThreshold 
          ? `响应时间较慢 (${Math.round(responseTime)}ms)`
          : '响应正常'
      }
    } catch (error: any) {
      return {
        status: 'error' as const,
        responseTime: 0,
        message: error.message || 'API连接失败'
      }
    }
  }

  /**
   * 检查内存使用情况
   */
  const checkMemoryHealth = async () => {
    if (typeof window === 'undefined' || !('memory' in performance)) {
      return {
        status: 'ok' as const,
        usage: 0,
        message: '内存信息不可用'
      }
    }

    const memory = (performance as any).memory
    const usagePercentage = Math.round((memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100)

    let status: 'ok' | 'warning' | 'error' = 'ok'
    let message = '内存使用正常'

    if (usagePercentage > config.memoryThreshold) {
      status = 'error'
      message = `内存使用率过高 (${usagePercentage}%)`
    } else if (usagePercentage > config.memoryThreshold * 0.8) {
      status = 'warning'
      message = `内存使用率较高 (${usagePercentage}%)`
    }

    return {
      status,
      usage: usagePercentage,
      message
    }
  }

  /**
   * 检查网络连接
   */
  const checkNetworkHealth = async () => {
    if (typeof navigator === 'undefined' || !('onLine' in navigator)) {
      return {
        status: 'ok' as const,
        message: '网络状态不可用'
      }
    }

    const isOnline = navigator.onLine
    
    return {
      status: isOnline ? 'ok' as const : 'error' as const,
      message: isOnline ? '网络连接正常' : '网络连接断开'
    }
  }

  /**
   * 计算整体健康状态
   */
  const calculateOverallStatus = (checks: HealthStatus['checks']): HealthStatus['status'] => {
    const hasError = Object.values(checks).some(check => check.status === 'error')
    const hasWarning = Object.values(checks).some(check => 
      'status' in check && check.status === 'warning'
    )

    if (hasError) return 'unhealthy'
    if (hasWarning) return 'degraded'
    return 'healthy'
  }

  /**
   * 开始定期健康检查
   */
  const startHealthCheck = () => {
    if (checkInterval.value) {
      clearInterval(checkInterval.value)
    }

    // 立即执行一次检查
    performHealthCheck()

    // 设置定期检查
    checkInterval.value = setInterval(() => {
      performHealthCheck()
    }, config.checkIntervalMs)
  }

  /**
   * 停止健康检查
   */
  const stopHealthCheck = () => {
    if (checkInterval.value) {
      clearInterval(checkInterval.value)
      checkInterval.value = null
    }
  }

  /**
   * 获取健康状态颜色
   */
  const getStatusColor = (status: HealthStatus['status']) => {
    switch (status) {
      case 'healthy':
        return 'text-green-600'
      case 'degraded':
        return 'text-yellow-600'
      case 'unhealthy':
        return 'text-red-600'
      default:
        return 'text-gray-600'
    }
  }

  /**
   * 获取健康状态文本
   */
  const getStatusText = (status: HealthStatus['status']) => {
    switch (status) {
      case 'healthy':
        return '健康'
      case 'degraded':
        return '降级'
      case 'unhealthy':
        return '不健康'
      default:
        return '未知'
    }
  }

  // 生命周期管理
  onMounted(() => {
    if (process.client) {
      startHealthCheck()
    }
  })

  onUnmounted(() => {
    stopHealthCheck()
  })

  return {
    // 状态
    healthStatus: readonly(healthStatus),
    isChecking: readonly(isChecking),
    lastCheckTime: readonly(lastCheckTime),

    // 方法
    performHealthCheck,
    startHealthCheck,
    stopHealthCheck,
    getStatusColor,
    getStatusText,

    // 配置
    config
  }
}
