<template>
  <div class="space-y-4">
    <!-- 操作按钮 -->
    <div class="flex items-center justify-between">
      <div class="flex gap-2">
        <!-- 批量操作按钮 -->
        <div v-if="selectedApiKeys.length > 0" class="flex gap-2 mr-4">
          <select
            v-model="batchStatus"
            class="rounded-lg border-gray-300 text-sm focus:border-blue-500 focus:ring-blue-500"
          >
            <option value="">选择状态</option>
            <option value="Active">启用</option>
            <option value="Disabled">禁用</option>
          </select>
          
          <UiButton 
            size="sm" 
            variant="warning" 
            :disabled="!batchStatus"
            @click="handleBatchUpdateStatus"
          >
            批量修改状态 ({{ selectedApiKeys.length }})
          </UiButton>
          
          <UiButton 
            size="sm" 
            variant="danger" 
            @click="handleBatchDelete"
          >
            批量删除 ({{ selectedApiKeys.length }})
          </UiButton>
        </div>
      </div>

      <div class="flex gap-2">
        <UiButton size="sm" @click="handleRefresh">
          <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
          刷新
        </UiButton>
      </div>
    </div>

    <!-- 表格 -->
    <UiTable
      :columns="columns"
      :data="apiKeys"
      :loading="loading"
      empty-text="暂无API密钥数据"
    >
      <!-- 复选框列 -->
      <template #select="{ row }">
        <input
          type="checkbox"
          :checked="selectedApiKeys.includes(row.id)"
          @change="toggleApiKeySelection(row.id)"
          class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
        />
      </template>

      <!-- 名称列 -->
      <template #name="{ value, row }">
        <div>
          <div class="font-medium text-gray-900">{{ value }}</div>
          <div v-if="row.description" class="text-sm text-gray-500">{{ row.description }}</div>
        </div>
      </template>

      <!-- 密钥列 -->
      <template #key="{ value }">
        <div class="font-mono text-sm">
          {{ maskApiKey(value) }}
        </div>
      </template>

      <!-- 状态列 -->
      <template #status="{ value, row }">
        <span
          :class="getStatusClasses(value, row)"
          class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
        >
          {{ getStatusText(value, row) }}
        </span>
      </template>

      <!-- 过期时间列 -->
      <template #expiresAt="{ value }">
        <div v-if="value" class="text-sm">
          <div :class="getExpirationClasses(value)">
            {{ formatDate(value) }}
          </div>
        </div>
        <span v-else class="text-gray-400">永不过期</span>
      </template>

      <!-- 创建时间列 -->
      <template #createdAt="{ value }">
        <span class="text-sm text-gray-600">
          {{ formatDate(value) }}
        </span>
      </template>

      <!-- 操作列 -->
      <template #actions="{ row }">
        <div class="flex items-center gap-1">
          <!-- 复制按钮 -->
          <UiButton
            size="xs"
            variant="ghost"
            @click="handleCopy(row)"
            title="复制密钥"
          >
            <svg class="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
            </svg>
          </UiButton>

          <!-- 状态切换按钮 -->
          <UiButton
            v-if="getActualStatus(row) === 'Active'"
            size="xs"
            variant="warning"
            @click="handleToggleStatus(row, 'Disabled')"
            title="禁用"
          >
            禁用
          </UiButton>
          
          <UiButton
            v-else-if="getActualStatus(row) === 'Disabled'"
            size="xs"
            variant="success"
            @click="handleToggleStatus(row, 'Active')"
            title="启用"
          >
            启用
          </UiButton>

          <!-- 编辑按钮 -->
          <UiButton
            size="xs"
            variant="ghost"
            @click="handleEdit(row)"
          >
            <svg class="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
          </UiButton>

          <!-- 删除按钮 -->
          <UiButton
            size="xs"
            variant="ghost"
            @click="handleDelete(row)"
          >
            <svg class="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
          </UiButton>
        </div>
      </template>
    </UiTable>
  </div>
</template>

<script setup lang="ts">
interface ApiKey {
  id: number
  name: string
  description?: string
  key: string
  status: string
  expiresAt?: string
  createdAt: string
}

interface Props {
  apiKeys: ApiKey[]
  loading?: boolean
}

interface Emits {
  refresh: []
  editApiKey: [apiKey: ApiKey]
  deleteApiKey: [apiKey: ApiKey]
  toggleStatus: [apiKey: ApiKey, status: string]
  copyApiKey: [apiKey: ApiKey]
  batchDelete: [apiKeyIds: number[]]
  batchUpdateStatus: [apiKeyIds: number[], status: string]
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const emit = defineEmits<Emits>()

// 批量操作相关
const selectedApiKeys = ref<number[]>([])
const batchStatus = ref('')

// 表格列配置
const columns = [
  { key: 'select', title: '', sortable: false, width: '50px', align: 'center' },
  { key: 'name', title: '名称', sortable: false, width: '200px' },
  { key: 'key', title: '密钥', sortable: false, width: '200px' },
  { key: 'status', title: '状态', sortable: false, width: '100px', align: 'center' },
  { key: 'expiresAt', title: '过期时间', sortable: false, width: '150px' },
  { key: 'createdAt', title: '创建时间', sortable: false, width: '150px' },
  { key: 'actions', title: '操作', sortable: false, width: '200px', align: 'center' }
]

// 方法
const handleRefresh = () => {
  emit('refresh')
}

const handleEdit = (apiKey: ApiKey) => {
  emit('editApiKey', apiKey)
}

const handleDelete = (apiKey: ApiKey) => {
  emit('deleteApiKey', apiKey)
}

const handleToggleStatus = (apiKey: ApiKey, status: string) => {
  emit('toggleStatus', apiKey, status)
}

const handleCopy = (apiKey: ApiKey) => {
  emit('copyApiKey', apiKey)
}

// 批量操作方法
const toggleApiKeySelection = (apiKeyId: number) => {
  const index = selectedApiKeys.value.indexOf(apiKeyId)
  if (index > -1) {
    selectedApiKeys.value.splice(index, 1)
  } else {
    selectedApiKeys.value.push(apiKeyId)
  }
}

const handleBatchDelete = () => {
  if (selectedApiKeys.value.length === 0) return
  
  if (confirm(`确定要删除选中的 ${selectedApiKeys.value.length} 个API密钥吗？`)) {
    emit('batchDelete', [...selectedApiKeys.value])
    selectedApiKeys.value = []
  }
}

const handleBatchUpdateStatus = () => {
  if (selectedApiKeys.value.length === 0 || !batchStatus.value) return
  
  const statusText = batchStatus.value === 'Active' ? '启用' : '禁用'
  if (confirm(`确定要将选中的 ${selectedApiKeys.value.length} 个API密钥${statusText}吗？`)) {
    emit('batchUpdateStatus', [...selectedApiKeys.value], batchStatus.value)
    selectedApiKeys.value = []
    batchStatus.value = ''
  }
}

// 工具方法
const maskApiKey = (key: string) => {
  if (key.length < 12) {
    return key.substring(0, 4) + '...'
  }
  return `${key.substring(0, 8)}...${key.substring(key.length - 4)}`
}

const formatDate = (dateString: string) => {
  if (!dateString) return '未知'
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN')
}

const isExpired = (apiKey: ApiKey) => {
  if (!apiKey.expiresAt) return false
  return new Date(apiKey.expiresAt) < new Date()
}

const getActualStatus = (apiKey: ApiKey) => {
  if (isExpired(apiKey)) return 'Expired'
  return apiKey.status
}

const getStatusText = (status: string, apiKey: ApiKey) => {
  const actualStatus = getActualStatus(apiKey)
  switch (actualStatus) {
    case 'Active': return '启用中'
    case 'Disabled': return '已禁用'
    case 'Expired': return '已过期'
    default: return '未知'
  }
}

const getStatusClasses = (status: string, apiKey: ApiKey) => {
  const actualStatus = getActualStatus(apiKey)
  switch (actualStatus) {
    case 'Active': return 'text-green-700 bg-green-100'
    case 'Disabled': return 'text-gray-700 bg-gray-100'
    case 'Expired': return 'text-red-700 bg-red-100'
    default: return 'text-gray-700 bg-gray-100'
  }
}

const getExpirationClasses = (expiresAt: string) => {
  const now = new Date()
  const expireDate = new Date(expiresAt)
  const diffDays = Math.ceil((expireDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
  
  if (diffDays < 0) {
    return 'text-red-600'
  } else if (diffDays <= 7) {
    return 'text-orange-600'
  } else {
    return 'text-gray-600'
  }
}
</script>
