/**
 * 创建账号接口
 * POST /api/admin/accounts
 */

import { prisma } from '../../../utils/db'

export default defineEventHandler(async (event) => {
  try {
    // 获取请求体
    const body = await readBody(event)
    const { poolId, content, notes, expiresAt } = body

    // 验证必填字段
    if (!poolId || typeof poolId !== 'string') {
      throw createError({
        statusCode: 400,
        statusMessage: 'Pool ID is required'
      })
    }

    if (!content || typeof content !== 'string' || content.trim().length === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Account content is required'
      })
    }

    // 验证号池是否存在
    const pool = await prisma.pool.findUnique({
      where: { id: poolId },
      include: {
        poolType: true
      }
    })

    if (!pool) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Pool not found'
      })
    }

    // 检查同一号池中是否已存在相同内容的账号
    const existingAccount = await prisma.account.findFirst({
      where: {
        poolId: poolId,
        content: content.trim()
      }
    })

    if (existingAccount) {
      throw createError({
        statusCode: 409,
        statusMessage: 'Account with this content already exists in the pool'
      })
    }

    // 处理过期时间
    let expiresAtDate = null
    if (expiresAt) {
      expiresAtDate = new Date(expiresAt)
      if (isNaN(expiresAtDate.getTime())) {
        throw createError({
          statusCode: 400,
          statusMessage: 'Invalid expires date format'
        })
      }
    }

    // 创建账号
    const newAccount = await prisma.account.create({
      data: {
        poolId: poolId,
        content: content.trim(),
        notes: notes ? notes.trim() : null,
        expiresAt: expiresAtDate,
        status: 'Available',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      include: {
        pool: {
          include: {
            poolType: true
          }
        }
      }
    })

    return {
      success: true,
      data: {
        id: newAccount.id,
        content: newAccount.content,
        status: newAccount.status,
        notes: newAccount.notes,
        expiresAt: newAccount.expiresAt,
        createdAt: newAccount.createdAt,
        updatedAt: newAccount.updatedAt,
        lastUsedAt: newAccount.lastUsedAt,
        pool: {
          id: newAccount.pool.id,
          name: newAccount.pool.name,
          poolType: {
            id: newAccount.pool.poolType.id,
            name: newAccount.pool.poolType.name
          }
        }
      }
    }
  } catch (error) {
    console.error('Failed to create account:', error)
    
    // 如果是已知的错误（如验证错误），直接抛出
    if (error.statusCode) {
      throw error
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to create account'
    })
  }
})
